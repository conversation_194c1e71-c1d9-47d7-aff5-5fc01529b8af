package dataconv

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"time"
)

// typeInfo 存储结构体类型的元信息，用于转换过程
type typeInfo struct {
	convertFields map[string]convertInfo     // 需要类型转换的字段信息
	rawDataField  string                     // 存储原始数据的字段名
	nestedFields  map[string]nestedFieldInfo // 嵌套结构体字段信息
	allFields     map[string]string          // 所有字段的映射 (mapstructure标签值 -> 字段名)
	excludeFields map[string]bool            // 需要从 rawlog 中排除的字段
}

// nestedFieldInfo 存储嵌套字段的元信息
type nestedFieldInfo struct {
	fieldType reflect.Type // 字段类型
	isSlice   bool         // 是否是切片类型
	fieldName string       // 结构体中的字段名
}

// convertInfo 存储类型转换信息
type convertInfo struct {
	fieldName string // 结构体中的字段名
	convType  string // 转换类型，如"stringToInt"
}

// mapstructure 专用类型信息缓存
var mapStructTypeCache sync.Map // map[reflect.Type]*structInfo

// MapToStruct 将map转换为结构体 - 高性能版本
func MapToStruct(data map[string]interface{}, target interface{}) error {
	rv := reflect.ValueOf(target)
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		return fmt.Errorf("target must be a non-nil pointer")
	}

	rv = rv.Elem()
	if rv.Kind() != reflect.Struct {
		return fmt.Errorf("target must be a pointer to struct")
	}

	return mapToStructWithRawLog(data, rv)
}

// getMapStructInfo 获取mapstructure专用的结构体类型信息，优先从缓存读取
func getMapStructInfo(t reflect.Type) *structInfo {
	if cached, ok := mapStructTypeCache.Load(t); ok {
		return cached.(*structInfo)
	}

	info := &structInfo{
		Fields:     make(map[string][]int),
		FieldInfos: make(map[string]*fieldInfo),
	}

	var analyzeStruct func(reflect.Type, []int, string)
	analyzeStruct = func(t reflect.Type, index []int, prefix string) {
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			fieldIndex := append(append([]int{}, index...), i)

			if field.PkgPath != "" && !field.Anonymous {
				continue
			}

			// 解析 convert 和 rawlog 标签
			convertTag := field.Tag.Get("convert")
			rawlogTag := field.Tag.Get("rawlog")

			// 检查是否是 rawlog 目标字段
			if rawlogTag == "target" && field.Type.Kind() == reflect.Map {
				info.RawLogIndex = fieldIndex
				continue // rawlog 目标字段不需要进一步处理
			} else if info.RawLogIndex == nil && field.Name == "RawLog" && field.Type.Kind() == reflect.Map {
				// 向后兼容：如果没有标记 rawlog:"target" 的字段，则使用名为 "RawLog" 的字段
				info.RawLogIndex = fieldIndex
				continue // RawLog 字段不需要进一步处理
			}

			tag := field.Tag.Get("mapconv")
			if tag == "-" {
				continue
			}

			fieldName := field.Name
			if tag != "" {
				if idx := strings.IndexByte(tag, ','); idx > 0 {
					fieldName = tag[:idx]
				} else if tag != "" {
					fieldName = tag
				}
			}

			// 创建字段信息
			fInfo := &fieldInfo{
				Index:         i,
				Name:          fieldName,
				ConvertType:   convertTag,
				ExcludeRawLog: rawlogTag == "exclude",
			}

			if field.Anonymous {
				fieldType := field.Type
				if fieldType.Kind() == reflect.Ptr {
					fieldType = fieldType.Elem()
				}

				if fieldType.Kind() == reflect.Struct {
					analyzeStruct(fieldType, fieldIndex, prefix)
					continue
				}
			}

			name := fieldName
			if prefix != "" {
				name = prefix + "." + name
			}

			// 设置字段类型信息
			fieldType := field.Type
			if fieldType.Kind() == reflect.Ptr {
				fInfo.IsPtr = true
				fInfo.ElemType = fieldType.Elem()
				fieldType = fInfo.ElemType
			}

			if fieldType.Kind() == reflect.Slice {
				fInfo.IsSlice = true
				fInfo.SliceElemType = fieldType.Elem()
				// 检查切片元素是否是结构体
				elemType := fInfo.SliceElemType
				if elemType.Kind() == reflect.Ptr {
					elemType = elemType.Elem()
				}
				if elemType.Kind() == reflect.Struct && elemType != reflect.TypeOf(time.Time{}) {
					fInfo.IsStruct = true
				}
			} else if fieldType.Kind() == reflect.Struct && fieldType != reflect.TypeOf(time.Time{}) {
				fInfo.IsStruct = true
			}

			info.Fields[fieldName] = fieldIndex
			info.FieldInfos[fieldName] = fInfo
		}
	}

	analyzeStruct(t, nil, "")
	mapStructTypeCache.Store(t, info)

	return info
}

// mapToStructWithRawLog 高性能map到结构体转换，使用专门的mapstructure缓存机制
func mapToStructWithRawLog(data map[string]interface{}, v reflect.Value) error {
	// 获取结构体元信息，使用mapstructure专用缓存
	t := v.Type()
	info := getMapStructInfo(t)

	// 初始化RawLog
	var rawLog reflect.Value
	if info.RawLogIndex != nil {
		rawLog = getFieldByPath(v, info.RawLogIndex)
		if rawLog.IsNil() {
			rawLog.Set(reflect.MakeMap(rawLog.Type()))
		}
	}

	// 单次遍历处理所有字段
	for key, value := range data {
		// 处理已知字段
		if fieldPath, ok := info.Fields[key]; ok {
			if err := setMapKnownField(v, key, value, fieldPath, info, rawLog); err != nil {
				return err
			}
		} else {
			// 处理未知字段
			if err := setMapUnknownField(key, value, rawLog); err != nil {
				return err
			}
		}
	}

	return nil
}

// setMapKnownField 处理已知字段的设置
func setMapKnownField(v reflect.Value, key string, value interface{}, fieldPath []int, info *structInfo, rawLog reflect.Value) error {
	field := getFieldByPath(v, fieldPath)
	fieldInfo := info.FieldInfos[key]

	// 处理切片类型
	if fieldInfo.IsSlice {
		if err := setMapSliceField(field, fieldInfo, key, value, rawLog); err != nil {
			return err
		}
		return nil
	}

	// 处理嵌套结构体
	if fieldInfo.IsStruct {
		if nestedMap, ok := value.(map[string]interface{}); ok {
			// 处理指针类型
			if field.Kind() == reflect.Ptr {
				if field.IsNil() {
					field.Set(reflect.New(field.Type().Elem()))
				}
				field = field.Elem()
			}

			if err := mapToStructWithRawLog(nestedMap, field); err != nil {
				return err
			}

			// 嵌套结构体也需要写入 rawLog（除非明确排除）
			if rawLog.IsValid() && !rawLog.IsNil() && !fieldInfo.ExcludeRawLog {
				rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(field.Interface()))
			}

			return nil
		}
	}

	// 处理普通字段
	return setMapSimpleField(field, fieldInfo, key, value, rawLog)
}

// setMapSimpleField 处理简单类型字段
func setMapSimpleField(field reflect.Value, fieldInfo *fieldInfo, key string, value interface{}, rawLog reflect.Value) error {
	needsConversion := fieldInfo.ConvertType != ""
	needsRawLog := rawLog.IsValid() && !rawLog.IsNil() && !fieldInfo.ExcludeRawLog

	var finalValue interface{}
	var err error

	if needsConversion {
		// 应用数据转换
		finalValue, err = convertValue(value, fieldInfo.ConvertType)
		if err != nil {
			return err
		}
	} else {
		finalValue = value
	}

	// 设置字段值
	if err := setFieldValue(field, finalValue); err != nil {
		return err
	}

	// 写入 rawLog
	if needsRawLog {
		rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(finalValue))
	}

	return nil
}

// setMapSliceField 处理切片类型字段
func setMapSliceField(field reflect.Value, fieldInfo *fieldInfo, key string, value interface{}, rawLog reflect.Value) error {
	sliceData, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("expected slice for field %s, got %T", key, value)
	}

	elemType := fieldInfo.SliceElemType
	isElemPtr := elemType.Kind() == reflect.Ptr
	if isElemPtr {
		elemType = elemType.Elem()
	}

	// 创建切片
	slice := reflect.MakeSlice(field.Type(), len(sliceData), len(sliceData))

	for i, item := range sliceData {
		elemValue := slice.Index(i)

		if fieldInfo.IsStruct && elemType.Kind() == reflect.Struct && elemType != reflect.TypeOf(time.Time{}) {
			// 处理结构体切片
			if itemMap, ok := item.(map[string]interface{}); ok {
				if isElemPtr {
					// 指针类型的结构体
					newElem := reflect.New(elemType)
					if err := mapToStructWithRawLog(itemMap, newElem.Elem()); err != nil {
						return err
					}
					elemValue.Set(newElem)
				} else {
					// 值类型的结构体
					if err := mapToStructWithRawLog(itemMap, elemValue); err != nil {
						return err
					}
				}
			}
		} else {
			// 处理基本类型切片
			if isElemPtr {
				newElem := reflect.New(elemType)
				if err := setFieldValue(newElem.Elem(), item); err != nil {
					return err
				}
				elemValue.Set(newElem)
			} else {
				if err := setFieldValue(elemValue, item); err != nil {
					return err
				}
			}
		}
	}

	field.Set(slice)

	// 写入 rawLog
	if rawLog.IsValid() && !rawLog.IsNil() && !fieldInfo.ExcludeRawLog {
		rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(field.Interface()))
	}

	return nil
}

// setMapUnknownField 处理未知字段
func setMapUnknownField(key string, value interface{}, rawLog reflect.Value) error {
	if rawLog.IsValid() && !rawLog.IsNil() {
		rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(value))
	}
	return nil
}


