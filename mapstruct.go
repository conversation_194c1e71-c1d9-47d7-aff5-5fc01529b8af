package dataconv

import (
	"reflect"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"
)

// typeInfo 存储结构体类型的元信息，用于转换过程
type typeInfo struct {
	convertFields map[string]convertInfo     // 需要类型转换的字段信息
	rawDataField  string                     // 存储原始数据的字段名
	nestedFields  map[string]nestedFieldInfo // 嵌套结构体字段信息
	allFields     map[string]string          // 所有字段的映射 (mapstructure标签值 -> 字段名)
	excludeFields map[string]bool            // 需要从 rawlog 中排除的字段
}

// nestedFieldInfo 存储嵌套字段的元信息
type nestedFieldInfo struct {
	fieldType reflect.Type // 字段类型
	isSlice   bool         // 是否是切片类型
	fieldName string       // 结构体中的字段名
}

// convertInfo 存储类型转换信息
type convertInfo struct {
	fieldName string // 结构体中的字段名
	convType  string // 转换类型，如"stringToInt"
}

// MapToStruct 将map转换为结构体
func MapToStruct(data map[string]interface{}, target interface{}) error {
	targetType := reflect.TypeOf(target).Elem()
	typeInfo := getTypeInfo(targetType)

	processedData, err := preprocessData(data, typeInfo)
	if err != nil {
		return err
	}

	decoderConfig := &mapstructure.DecoderConfig{
		Result:  target,
		TagName: "mapstructure",
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			mapstructure.StringToTimeHookFunc("2006-01-02"),
		),
	}

	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		return err
	}

	if err := decoder.Decode(processedData); err != nil {
		return err
	}

	processRawData(data, target, typeInfo)
	return nil
}

// getTypeInfo 获取结构体类型的元信息
func getTypeInfo(typ reflect.Type) typeInfo {
	info := typeInfo{
		convertFields: make(map[string]convertInfo),
		nestedFields:  make(map[string]nestedFieldInfo),
		allFields:     make(map[string]string),
		excludeFields: make(map[string]bool),
	}

	if typ.Kind() != reflect.Struct {
		return info
	}

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		mapTag := field.Tag.Get("mapstructure")
		if mapTag == "-" {
			continue
		}

		fieldName := mapTag
		if fieldName == "" {
			fieldName = field.Name
		} else {
			fieldName = strings.Split(fieldName, ",")[0]
		}

		// 记录所有字段
		info.allFields[fieldName] = field.Name

		// 检查是否是 RawLog 字段（与 decode.go 保持一致）
		if field.Name == "RawLog" && field.Type.Kind() == reflect.Map {
			info.rawDataField = field.Name
		}

		// 检查 rawlog 标签，与 decode.go 保持一致
		rawlogTag := field.Tag.Get("rawlog")
		if rawlogTag == "exclude" {
			info.excludeFields[fieldName] = true
		}

		convertTag := field.Tag.Get("convert")
		if convertTag != "" {
			info.convertFields[fieldName] = convertInfo{
				fieldName: field.Name,
				convType:  convertTag,
			}
		}

		fieldType := field.Type
		isSlice := false

		if fieldType.Kind() == reflect.Ptr {
			fieldType = fieldType.Elem()
		}

		if fieldType.Kind() == reflect.Slice {
			fieldType = fieldType.Elem()
			isSlice = true

			if fieldType.Kind() == reflect.Ptr {
				fieldType = fieldType.Elem()
			}
		}

		if fieldType.Kind() == reflect.Struct && fieldType != reflect.TypeOf(time.Time{}) {
			info.nestedFields[fieldName] = nestedFieldInfo{
				fieldType: fieldType,
				isSlice:   isSlice,
				fieldName: field.Name,
			}
		}
	}

	return info
}

// preprocessData 预处理map数据
func preprocessData(data map[string]interface{}, info typeInfo) (map[string]interface{}, error) {
	// 1. 创建结果map，初始复制原始数据
	result := make(map[string]interface{}, len(data))
	for k, v := range data {
		result[k] = v
	}

	// 2. 处理需要类型转换的字段
	for fieldName, convInfo := range info.convertFields {
		if val, exists := result[fieldName]; exists {
			// 执行实际类型转换
			if converted, err := convertValue(val, convInfo.convType); err == nil {
				result[fieldName] = converted
			}
		}
	}

	// 3. 处理嵌套结构体字段
	for fieldName, nestedInfo := range info.nestedFields {
		if val, exists := result[fieldName]; exists {
			// 获取嵌套结构体的类型信息
			nestedTypeInfo := getTypeInfo(nestedInfo.fieldType)

			if nestedInfo.isSlice {
				// 3.1 处理切片类型的嵌套结构体
				if sliceVal, ok := val.([]map[string]interface{}); ok {
					processedSlice := make([]map[string]interface{}, len(sliceVal))
					for i, item := range sliceVal {
						// 递归处理每个切片元素
						processed, err := preprocessData(item, nestedTypeInfo)
						if err != nil {
							return nil, err
						}
						processedSlice[i] = processed
					}
					result[fieldName] = processedSlice
				}
			} else {
				// 3.2 处理非切片类型的嵌套结构体
				if nestedMap, ok := val.(map[string]interface{}); ok {
					// 递归处理嵌套map
					processed, err := preprocessData(nestedMap, nestedTypeInfo)
					if err != nil {
						return nil, err
					}
					result[fieldName] = processed
				}
			}
		}
	}

	return result, nil
}

// processRawData 处理原始数据，将其存储到目标结构体的指定字段中
func processRawData(data map[string]interface{}, target interface{}, info typeInfo) {
	// 获取目标结构体的反射值
	val := reflect.ValueOf(target).Elem()

	// 1. 处理原始数据字段(rawDataField) - 与 decode.go 保持一致
	if info.rawDataField != "" {
		// 获取原始数据字段的反射值
		rawDataField := val.FieldByName(info.rawDataField)

		// 检查字段是否有效、可设置且为map类型
		if rawDataField.IsValid() && rawDataField.CanSet() && rawDataField.Kind() == reflect.Map {
			// 初始化 RawLog map
			if rawDataField.IsNil() {
				rawDataField.Set(reflect.MakeMap(rawDataField.Type()))
			}

			// 遍历原始数据，将当前层级的所有字段写入 rawData（除非明确排除）
			for k, v := range data {
				// 检查是否需要排除该字段
				if info.excludeFields[k] {
					continue
				}

				// 处理类型转换后的值
				if convInfo, needsConversion := info.convertFields[k]; needsConversion {
					if convertedValue, err := convertValue(v, convInfo.convType); err == nil {
						rawDataField.SetMapIndex(reflect.ValueOf(k), reflect.ValueOf(convertedValue))
					} else {
						// 转换失败时使用原值
						rawDataField.SetMapIndex(reflect.ValueOf(k), reflect.ValueOf(v))
					}
				} else {
					// 不需要转换的字段直接写入
					rawDataField.SetMapIndex(reflect.ValueOf(k), reflect.ValueOf(v))
				}
			}
		}
	}

	// 2. 处理嵌套结构体的原始数据
	for fieldName, nestedInfo := range info.nestedFields {
		// 检查字段是否存在原始数据中
		if nestedVal, exists := data[fieldName]; exists {
			// 获取嵌套字段的反射值
			nestedField := val.FieldByName(nestedInfo.fieldName)
			if !nestedField.IsValid() {
				continue
			}

			if nestedInfo.isSlice {
				// 2.1 处理切片类型的嵌套结构体
				sliceVal, ok := nestedVal.([]map[string]interface{})
				if !ok {
					continue
				}

				// 确保嵌套字段是切片类型且不为nil
				if nestedField.Kind() != reflect.Slice || nestedField.IsNil() {
					continue
				}

				// 遍历切片中的每个元素
				for i := 0; i < nestedField.Len() && i < len(sliceVal); i++ {
					// 获取当前元素的反射值
					itemVal := nestedField.Index(i)

					// 处理指针类型的元素
					if itemVal.Kind() == reflect.Ptr {
						if itemVal.IsNil() {
							continue
						}
						itemVal = itemVal.Elem()
					}

					// 确保元素是结构体类型
					if itemVal.Kind() != reflect.Struct {
						continue
					}

					// 获取对应的原始数据
					itemMap := sliceVal[i]

					// 获取元素类型的元信息并递归处理
					elemTypeInfo := getTypeInfo(itemVal.Type())
					processRawData(itemMap, itemVal.Addr().Interface(), elemTypeInfo)
				}
			} else {
				// 2.2 处理非切片类型的嵌套结构体
				if nestedField.Kind() == reflect.Ptr {
					if nestedField.IsNil() {
						continue
					}
					nestedField = nestedField.Elem()
				}

				if nestedField.Kind() == reflect.Struct {
					if nestedMap, ok := nestedVal.(map[string]interface{}); ok {
						// 递归处理嵌套结构体的原始数据
						nestedTypeInfo := getTypeInfo(nestedField.Type())
						processRawData(nestedMap, nestedField.Addr().Interface(), nestedTypeInfo)
					}
				}
			}

			// 3. 将嵌套结构体数据写入父级 RawLog（与 decode.go 保持一致）
			if info.rawDataField != "" && !info.excludeFields[fieldName] {
				rawDataField := val.FieldByName(info.rawDataField)
				if rawDataField.IsValid() && rawDataField.CanSet() && rawDataField.Kind() == reflect.Map {
					if !rawDataField.IsNil() {
						// 获取嵌套字段的当前值并写入 RawLog
						nestedFieldValue := val.FieldByName(nestedInfo.fieldName)
						if nestedFieldValue.IsValid() {
							rawDataField.SetMapIndex(reflect.ValueOf(fieldName), reflect.ValueOf(nestedFieldValue.Interface()))
						}
					}
				}
			}
		}
	}
}
