package dataconv

import (
	"reflect"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
)

func TestOptimizedMapToStruct(t *testing.T) {
	Convey("Test OptimizedMapToStruct functionality", t, func() {
		
		Convey("Simple data should use fast path", func() {
			type SimpleStruct struct {
				Name   string                 `mapconv:"name"`
				Age    int                    `mapconv:"age"`
				Active bool                   `mapconv:"active"`
				RawLog map[string]interface{} `rawlog:"target"`
			}

			data := map[string]interface{}{
				"name":   "Alice",
				"age":    25,
				"active": true,
				"extra":  "should be in rawlog",
			}

			var result SimpleStruct
			err := OptimizedMapToStruct(data, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "Alice")
			So(result.Age, ShouldEqual, 25)
			So(result.Active, ShouldBeTrue)
			So(result.RawLog["extra"], ShouldEqual, "should be in rawlog")
		})

		<PERSON>vey("Moderate complexity should use optimized mapstructure", func() {
			type NestedStruct struct {
				Field1 string                 `mapconv:"field1"`
				Field2 int                    `mapconv:"field2" convert:"stringToNumber"`
				RawLog map[string]interface{} `rawlog:"target"`
			}

			type ModerateStruct struct {
				Name     string                 `mapconv:"name"`
				Age      string                 `mapconv:"age" convert:"numberToString"`
				Nested   NestedStruct           `mapconv:"nested"`
				Items    []string               `mapconv:"items"`
				Secret   string                 `mapconv:"secret" rawlog:"exclude"`
				RawLog   map[string]interface{} `rawlog:"target"`
			}

			data := map[string]interface{}{
				"name":   "Bob",
				"age":    30,
				"secret": "confidential",
				"nested": map[string]interface{}{
					"field1": "nested_value",
					"field2": "42",
					"extra":  "nested_extra",
				},
				"items": []string{"item1", "item2", "item3"},
				"extra": "root_extra",
			}

			var result ModerateStruct
			err := OptimizedMapToStruct(data, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "Bob")
			So(result.Age, ShouldEqual, "30")
			So(result.Secret, ShouldEqual, "confidential")
			So(result.Items, ShouldResemble, []string{"item1", "item2", "item3"})
			
			// 验证嵌套结构
			So(result.Nested.Field1, ShouldEqual, "nested_value")
			So(result.Nested.Field2, ShouldEqual, 42)
			So(result.Nested.RawLog["extra"], ShouldEqual, "nested_extra")

			// 验证 RawLog
			So(result.RawLog["extra"], ShouldEqual, "root_extra")
			So(result.RawLog, ShouldNotContainKey, "secret") // 排除字段
		})

		Convey("Complex data should use standard mapstructure", func() {
			type DeepNested struct {
				Level3Field string                 `mapconv:"level3_field"`
				RawLog      map[string]interface{} `rawlog:"target"`
			}

			type Level2Nested struct {
				Level2Field string                 `mapconv:"level2_field"`
				Deep        DeepNested             `mapconv:"deep"`
				RawLog      map[string]interface{} `rawlog:"target"`
			}

			type Level1Nested struct {
				Level1Field string                 `mapconv:"level1_field"`
				Level2      Level2Nested           `mapconv:"level2"`
				RawLog      map[string]interface{} `rawlog:"target"`
			}

			type ComplexStruct struct {
				Name    string                 `mapconv:"name"`
				Level1  Level1Nested           `mapconv:"level1"`
				Items1  []string               `mapconv:"items1"`
				Items2  []int                  `mapconv:"items2"`
				Items3  []bool                 `mapconv:"items3"`
				RawLog  map[string]interface{} `rawlog:"target"`
			}

			data := map[string]interface{}{
				"name": "Complex",
				"level1": map[string]interface{}{
					"level1_field": "l1_value",
					"level2": map[string]interface{}{
						"level2_field": "l2_value",
						"deep": map[string]interface{}{
							"level3_field": "l3_value",
							"deep_extra":   "deep_data",
						},
						"l2_extra": "l2_data",
					},
					"l1_extra": "l1_data",
				},
				"items1": []string{"a", "b", "c"},
				"items2": []int{1, 2, 3},
				"items3": []bool{true, false, true},
				"extra":  "root_data",
			}

			var result ComplexStruct
			err := OptimizedMapToStruct(data, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "Complex")
			So(result.Items1, ShouldResemble, []string{"a", "b", "c"})
			So(result.Items2, ShouldResemble, []int{1, 2, 3})
			So(result.Items3, ShouldResemble, []bool{true, false, true})

			// 验证深度嵌套
			So(result.Level1.Level1Field, ShouldEqual, "l1_value")
			So(result.Level1.Level2.Level2Field, ShouldEqual, "l2_value")
			So(result.Level1.Level2.Deep.Level3Field, ShouldEqual, "l3_value")

			// 验证各层 RawLog
			So(result.RawLog["extra"], ShouldEqual, "root_data")
			So(result.Level1.RawLog["l1_extra"], ShouldEqual, "l1_data")
			So(result.Level1.Level2.RawLog["l2_extra"], ShouldEqual, "l2_data")
			So(result.Level1.Level2.Deep.RawLog["deep_extra"], ShouldEqual, "deep_data")
		})

		Convey("Type conversion should work correctly", func() {
			type ConvertStruct struct {
				StringToInt    int       `mapconv:"string_to_int" convert:"stringToNumber"`
				NumberToString string    `mapconv:"number_to_string" convert:"numberToString"`
				StringToTime   time.Time `mapconv:"string_to_time" convert:"stringToTime"`
				RawLog         map[string]interface{} `rawlog:"target"`
			}

			data := map[string]interface{}{
				"string_to_int":    "123",
				"number_to_string": 456,
				"string_to_time":   "2025-01-01T10:00:00Z",
				"extra":            "test_data",
			}

			var result ConvertStruct
			err := OptimizedMapToStruct(data, &result)
			So(err, ShouldBeNil)

			So(result.StringToInt, ShouldEqual, 123)
			So(result.NumberToString, ShouldEqual, "456")
			So(result.StringToTime.Year(), ShouldEqual, 2025)
			So(result.StringToTime.Month(), ShouldEqual, 1)
			So(result.StringToTime.Day(), ShouldEqual, 1)

			// 验证转换后的值写入 RawLog
			So(result.RawLog["string_to_int"], ShouldEqual, int64(123))
			So(result.RawLog["number_to_string"], ShouldEqual, "456")
			So(result.RawLog["extra"], ShouldEqual, "test_data")
		})

		Convey("Custom rawlog field name should work", func() {
			type CustomRawLogStruct struct {
				Name          string                 `mapconv:"name"`
				Age           int                    `mapconv:"age"`
				MyCustomField map[string]interface{} `mapconv:"-" rawlog:"target"`
			}

			data := map[string]interface{}{
				"name":  "Charlie",
				"age":   35,
				"extra": "custom_data",
			}

			var result CustomRawLogStruct
			err := OptimizedMapToStruct(data, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "Charlie")
			So(result.Age, ShouldEqual, 35)
			So(result.MyCustomField["extra"], ShouldEqual, "custom_data")
			So(result.MyCustomField["name"], ShouldEqual, "Charlie")
			So(result.MyCustomField["age"], ShouldEqual, 35)
		})
	})
}

func TestDataComplexityAnalysis(t *testing.T) {
	Convey("Test data complexity analysis", t, func() {
		
		Convey("Simple data should be detected correctly", func() {
			data := map[string]interface{}{
				"field1": "value1",
				"field2": 123,
				"field3": true,
			}

			complexity := analyzeDataComplexity(data)
			So(complexity.isSimple(), ShouldBeTrue)
			So(complexity.isModerate(), ShouldBeTrue)
		})

		Convey("Moderate data should be detected correctly", func() {
			data := map[string]interface{}{
				"field1": "value1",
				"field2": 123,
				"nested": map[string]interface{}{
					"nested_field": "nested_value",
				},
				"slice": []string{"a", "b", "c"},
			}

			complexity := analyzeDataComplexity(data)
			So(complexity.isSimple(), ShouldBeFalse)
			So(complexity.isModerate(), ShouldBeTrue)
		})

		Convey("Complex data should be detected correctly", func() {
			data := map[string]interface{}{
				"field1": "value1",
				"field2": 123,
				"nested1": map[string]interface{}{
					"nested_field": "nested_value",
					"deep_nested": map[string]interface{}{
						"deep_field": "deep_value",
					},
				},
				"nested2": map[string]interface{}{
					"another_nested": "value",
				},
				"nested3": map[string]interface{}{
					"third_nested": "value",
				},
				"nested4": map[string]interface{}{
					"fourth_nested": "value",
				},
				"slice1": []string{"a", "b", "c"},
				"slice2": []int{1, 2, 3},
				"slice3": []bool{true, false},
			}

			complexity := analyzeDataComplexity(data)
			So(complexity.isSimple(), ShouldBeFalse)
			So(complexity.isModerate(), ShouldBeFalse)
		})
	})
}

func TestOptimizedStructInfoCaching(t *testing.T) {
	Convey("Test optimized struct info caching", t, func() {
		type TestStruct struct {
			Name   string                 `mapconv:"name"`
			Age    int                    `mapconv:"age" convert:"stringToNumber"`
			Secret string                 `mapconv:"secret" rawlog:"exclude"`
			RawLog map[string]interface{} `rawlog:"target"`
		}

		// 第一次获取
		info1 := getOptimizedStructInfo(reflect.TypeOf(TestStruct{}))
		
		// 第二次获取应该从缓存中读取
		info2 := getOptimizedStructInfo(reflect.TypeOf(TestStruct{}))
		
		// 应该是同一个对象（指针相等）
		So(info1, ShouldEqual, info2)
		
		// 验证信息正确性
		So(info1.rawLogField, ShouldEqual, "RawLog")
		So(info1.convertFields["age"], ShouldEqual, "stringToNumber")
		So(info1.excludeFields["secret"], ShouldBeTrue)
		So(info1.fieldNames["name"], ShouldEqual, "Name")
	})
}
