package dataconv

import (
	"reflect"
	"strings"
	"sync"
	"time"

	"github.com/mitchellh/mapstructure"
)

// optimizedStructInfo 存储优化的结构体信息
type optimizedStructInfo struct {
	convertFields map[string]string // 字段名 -> 转换类型
	rawLogField   string            // RawLog 字段名
	excludeFields map[string]bool   // 需要从 RawLog 排除的字段
	fieldNames    map[string]string // mapconv 标签名 -> 实际字段名
}

// mapstructure 优化缓存
var mapstructureCache sync.Map

// dataComplexity 数据复杂度分析结果
type dataComplexity struct {
	fieldCount    int
	nestedCount   int
	sliceCount    int
	hasConversion bool
}

// isSimple 判断是否为简单数据
func (c *dataComplexity) isSimple() bool {
	return c.fieldCount <= 5 && c.nestedCount == 0 && c.sliceCount == 0
}

// isModerate 判断是否为中等复杂度数据
func (c *dataComplexity) isModerate() bool {
	return c.fieldCount <= 20 && c.nestedCount <= 3 && c.sliceCount <= 2
}

// OptimizedMapToStruct 优化版本的 map 转 struct，基于 mapstructure
func OptimizedMapToStruct(data map[string]interface{}, target interface{}) error {
	// 分析数据复杂度
	complexity := analyzeDataComplexity(data)
	
	// 根据复杂度选择最优策略
	switch {
	case complexity.isSimple():
		// 简单数据使用自实现（最快）
		return MapToStruct(data, target)
		
	case complexity.isModerate():
		// 中等复杂度使用优化的 mapstructure
		return optimizedMapstructureConvert(data, target)
		
	default:
		// 复杂数据使用标准 mapstructure（最稳定）
		return standardMapstructureConvert(data, target)
	}
}

// analyzeDataComplexity 分析数据复杂度
func analyzeDataComplexity(data map[string]interface{}) *dataComplexity {
	complexity := &dataComplexity{
		fieldCount: len(data),
	}
	
	for _, value := range data {
		switch v := value.(type) {
		case map[string]interface{}:
			complexity.nestedCount++
			// 递归分析嵌套数据
			nested := analyzeDataComplexity(v)
			complexity.fieldCount += nested.fieldCount
			complexity.nestedCount += nested.nestedCount
			complexity.sliceCount += nested.sliceCount
			
		case []interface{}:
			complexity.sliceCount++
			// 检查切片元素是否包含结构体
			if len(v) > 0 {
				if _, isMap := v[0].(map[string]interface{}); isMap {
					complexity.nestedCount++
				}
			}
		}
	}
	
	return complexity
}

// optimizedMapstructureConvert 使用优化的 mapstructure 进行转换
func optimizedMapstructureConvert(data map[string]interface{}, target interface{}) error {
	// 对于中等复杂度的数据，我们使用混合方案：
	// 1. 使用 mapstructure 进行主要的结构体映射（利用其稳定性）
	// 2. 使用自定义逻辑处理 RawLog 和类型转换

	// 首先使用 mapstructure 进行基础转换
	config := &mapstructure.DecoderConfig{
		Result:           target,
		TagName:          "mapconv",
		WeaklyTypedInput: true,
		ErrorUnused:      false,
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			optimizedStringToTimeHook(),
			mapstructure.StringToTimeHookFunc(time.RFC3339),
		),
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return err
	}

	if err := decoder.Decode(data); err != nil {
		return err
	}

	// 然后使用自定义逻辑处理 RawLog 和类型转换
	return processRawLogRecursively(data, target)
}

// standardMapstructureConvert 使用标准 mapstructure 进行转换
func standardMapstructureConvert(data map[string]interface{}, target interface{}) error {
	// 对于复杂数据，我们仍然使用自实现版本以确保 RawLog 功能完整
	// 因为标准 mapstructure 无法处理我们的自定义 RawLog 逻辑
	return MapToStruct(data, target)
}

// getOptimizedStructInfo 获取优化的结构体信息（带缓存）
func getOptimizedStructInfo(t reflect.Type) *optimizedStructInfo {
	if cached, ok := mapstructureCache.Load(t); ok {
		return cached.(*optimizedStructInfo)
	}
	
	info := analyzeStructForMapstructure(t)
	mapstructureCache.Store(t, info)
	return info
}

// analyzeStructForMapstructure 分析结构体信息用于 mapstructure 优化
func analyzeStructForMapstructure(t reflect.Type) *optimizedStructInfo {
	info := &optimizedStructInfo{
		convertFields: make(map[string]string),
		excludeFields: make(map[string]bool),
		fieldNames:    make(map[string]string),
	}
	
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		
		// 检查 rawlog 标签
		rawlogTag := field.Tag.Get("rawlog")
		
		// 检查是否是 rawlog 目标字段
		if rawlogTag == "target" && field.Type.Kind() == reflect.Map {
			info.rawLogField = field.Name
			continue
		} else if info.rawLogField == "" && field.Name == "RawLog" && field.Type.Kind() == reflect.Map {
			// 向后兼容
			info.rawLogField = field.Name
			continue
		}
		
		// 获取字段名
		mapconvTag := field.Tag.Get("mapconv")
		if mapconvTag == "-" {
			continue
		}
		
		fieldName := field.Name
		if mapconvTag != "" {
			if idx := strings.IndexByte(mapconvTag, ','); idx > 0 {
				fieldName = mapconvTag[:idx]
			} else {
				fieldName = mapconvTag
			}
		}
		
		info.fieldNames[fieldName] = field.Name
		
		// 检查转换标签
		convertTag := field.Tag.Get("convert")
		if convertTag != "" {
			info.convertFields[fieldName] = convertTag
		}
		
		// 检查排除标签
		if rawlogTag == "exclude" {
			info.excludeFields[fieldName] = true
		}
	}
	
	return info
}

// preprocessForMapstructure 为 mapstructure 预处理数据
func preprocessForMapstructure(data map[string]interface{}, info *optimizedStructInfo) (map[string]interface{}, map[string]interface{}) {
	processedData := make(map[string]interface{})
	rawLogData := make(map[string]interface{})
	
	for key, value := range data {
		// 检查是否需要类型转换
		if convertType, needsConversion := info.convertFields[key]; needsConversion {
			if convertedValue, err := convertValue(value, convertType); err == nil {
				processedData[key] = convertedValue
			} else {
				processedData[key] = value
			}
		} else {
			processedData[key] = value
		}
		
		// 收集 RawLog 数据（除非明确排除）
		if !info.excludeFields[key] {
			if convertType, needsConversion := info.convertFields[key]; needsConversion {
				if convertedValue, err := convertValue(value, convertType); err == nil {
					rawLogData[key] = convertedValue
				} else {
					rawLogData[key] = value
				}
			} else {
				rawLogData[key] = value
			}
		}
	}
	
	return processedData, rawLogData
}

// createOptimizedDecoder 创建优化的 mapstructure 解码器
func createOptimizedDecoder(target interface{}) (*mapstructure.Decoder, error) {
	config := &mapstructure.DecoderConfig{
		Result:  target,
		TagName: "mapconv",
		
		// 添加高性能转换 Hook
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			optimizedStringToTimeHook(),
			mapstructure.StringToTimeHookFunc(time.RFC3339),
		),
		
		// 启用弱类型转换以提升性能
		WeaklyTypedInput: true,
		
		// 忽略未使用字段以避免错误
		ErrorUnused: false,
	}
	
	return mapstructure.NewDecoder(config)
}

// optimizedStringToTimeHook 优化的字符串转时间 Hook
func optimizedStringToTimeHook() mapstructure.DecodeHookFunc {
	// 预编译常用时间格式以提升性能
	timeFormats := []string{
		"2006-01-02",
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02T15:04:05-07:00",
		time.RFC3339,
		time.RFC3339Nano,
	}
	
	return func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
		if f.Kind() != reflect.String || t != reflect.TypeOf(time.Time{}) {
			return data, nil
		}
		
		str := data.(string)
		
		// 快速路径：尝试常用格式
		for _, format := range timeFormats {
			if parsed, err := time.Parse(format, str); err == nil {
				return parsed, nil
			}
		}
		
		return data, nil
	}
}

// setRawLogData 设置 RawLog 数据
func setRawLogData(target interface{}, rawLogData map[string]interface{}, info *optimizedStructInfo) error {
	if info.rawLogField == "" || len(rawLogData) == 0 {
		return nil
	}
	
	v := reflect.ValueOf(target).Elem()
	rawLogField := v.FieldByName(info.rawLogField)
	
	if !rawLogField.IsValid() || !rawLogField.CanSet() {
		return nil
	}
	
	if rawLogField.IsNil() {
		rawLogField.Set(reflect.MakeMap(rawLogField.Type()))
	}
	
	for k, val := range rawLogData {
		rawLogField.SetMapIndex(reflect.ValueOf(k), reflect.ValueOf(val))
	}
	
	return nil
}

// processRawLogRecursively 递归处理 RawLog 和类型转换
func processRawLogRecursively(data map[string]interface{}, target interface{}) error {
	v := reflect.ValueOf(target).Elem()
	t := v.Type()
	structInfo := getOptimizedStructInfo(t)

	// 处理当前层级的 RawLog
	if structInfo.rawLogField != "" {
		rawLogField := v.FieldByName(structInfo.rawLogField)
		if rawLogField.IsValid() && rawLogField.CanSet() {
			if rawLogField.IsNil() {
				rawLogField.Set(reflect.MakeMap(rawLogField.Type()))
			}

			// 写入所有字段到 RawLog（除非明确排除）
			for key, value := range data {
				if !structInfo.excludeFields[key] {
					// 应用类型转换
					if convertType, needsConversion := structInfo.convertFields[key]; needsConversion {
						if convertedValue, err := convertValue(value, convertType); err == nil {
							rawLogField.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(convertedValue))
						} else {
							rawLogField.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(value))
						}
					} else {
						rawLogField.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(value))
					}
				}
			}
		}
	}

	// 递归处理嵌套结构体
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		fieldValue := v.Field(i)

		// 跳过 RawLog 字段
		if field.Name == structInfo.rawLogField {
			continue
		}

		// 获取字段名
		mapconvTag := field.Tag.Get("mapconv")
		if mapconvTag == "-" {
			continue
		}

		fieldName := field.Name
		if mapconvTag != "" {
			if idx := strings.IndexByte(mapconvTag, ','); idx > 0 {
				fieldName = mapconvTag[:idx]
			} else {
				fieldName = mapconvTag
			}
		}

		// 检查数据中是否有对应的嵌套数据
		if nestedData, exists := data[fieldName]; exists {
			if nestedMap, ok := nestedData.(map[string]interface{}); ok {
				// 处理嵌套结构体
				if field.Type.Kind() == reflect.Struct {
					if err := processRawLogRecursively(nestedMap, fieldValue.Addr().Interface()); err != nil {
						return err
					}
				} else if field.Type.Kind() == reflect.Ptr && field.Type.Elem().Kind() == reflect.Struct {
					if !fieldValue.IsNil() {
						if err := processRawLogRecursively(nestedMap, fieldValue.Interface()); err != nil {
							return err
						}
					}
				}
			}
		}
	}

	return nil
}
