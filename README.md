# DataConv - 高性能 MessagePack 数据转换库

[![Go Version](https://img.shields.io/badge/Go-%3E%3D%201.24-blue.svg)](https://golang.org/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

DataConv 是一个专为 Go 语言设计的高性能 MessagePack 数据解码和转换库，提供了灵活的结构体映射、类型转换和原始数据保留功能。

## 🚀 核心特性

- **高性能 MessagePack 解码**: 基于 `vmihailenco/msgpack` 的优化解码器
- **智能类型转换**: 支持数字↔字符串、字符串→时间的自动转换
- **原始数据保留**: 自动将未映射字段存储到 RawLog 中
- **嵌套结构体支持**: 完整支持复杂嵌套结构、结构体切片的解码
- **类型缓存优化**: 内置结构体类型信息缓存，提升性能
- **灵活标签控制**: 通过标签精确控制字段行为

## 📦 安装

```bash
go get -u dataconv
```

## 🔧 快速开始

### 基础用法

```go
package main

import (
    "fmt"
    "github.com/vmihailenco/msgpack/v5"
    "dataconv"
)

type User struct {
    Name   string                 `msgpack:"name"`
    Age    int                    `msgpack:"age"`
    RawLog map[string]interface{} `msgpack:"-"`
}

func main() {
    // 准备 MessagePack 数据
    data := map[string]interface{}{
        "name": "Alice",
        "age":  25,
        "city": "Beijing", // 未定义字段，将存储到 RawLog
    }
    
    msgpackData, _ := msgpack.Marshal(data)
    
    // 解码
    var user User
    err := dataconv.MsgpackDecode(msgpackData, &user)
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("Name: %s, Age: %d\n", user.Name, user.Age)
    fmt.Printf("RawLog: %+v\n", user.RawLog) // 输出: map[city:Beijing name:Alice age:25]
}
```

### 类型转换功能

```go
type ConvertExample struct {
    UserID    string  `msgpack:"user_id" convert:"numberToString"`
    Score     int64   `msgpack:"score" convert:"stringToNumber"`
    Balance   float64 `msgpack:"balance" convert:"stringToNumber"`
    RawLog    map[string]interface{} `msgpack:"-"`
}

func main() {
    data := map[string]interface{}{
        "user_id": 12345,      // 数字 -> 字符串
        "score":   "95",       // 字符串 -> 数字
        "balance": "1234.56",  // 字符串 -> 浮点数
    }
    
    msgpackData, _ := msgpack.Marshal(data)
    
    var result ConvertExample
    err := dataconv.MsgpackDecode(msgpackData, &result)
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("UserID: %s (string)\n", result.UserID)     // "12345"
    fmt.Printf("Score: %d (int64)\n", result.Score)        // 95
    fmt.Printf("Balance: %.2f (float64)\n", result.Balance) // 1234.56
}
```

### 嵌套结构体和 RawLog 排除

```go
type Address struct {
    Street  string                 `msgpack:"street"`
    City    string                 `msgpack:"city"`
    RawLog  map[string]interface{} `msgpack:"-" rawlog:"target"`
}

type Person struct {
    Name     string                 `msgpack:"name"`
    Address  Address                `msgpack:"address"`
    Password string                 `msgpack:"password" rawlog:"exclude"` // 不写入 RawLog
    RawLog   map[string]interface{} `msgpack:"-" rawlog:"target"`
}
```

### 自定义 RawLog 字段名

```go
type User struct {
    Name        string                 `msgpack:"name"`
    Age         int                    `msgpack:"age"`
    Secret      string                 `msgpack:"secret" rawlog:"exclude"`
    MyCustomLog map[string]interface{} `msgpack:"-" rawlog:"target"` // 自定义字段名
}
```

### 时间转换和切片支持

```go
type Event struct {
    Name      string                 `msgpack:"name"`
    CreatedAt time.Time              `msgpack:"created_at" convert:"stringToTime"`
    Tags      []string               `msgpack:"tags"`
    Users     []User                 `msgpack:"users"`     // 结构体切片
    UserPtrs  []*User                `msgpack:"user_ptrs"` // 指针结构体切片
    RawLog    map[string]interface{} `msgpack:"-" rawlog:"target"`
}
```

## 📋 标签说明

### msgpack 标签
- `msgpack:"field_name"`: 指定 MessagePack 中的字段名
- `msgpack:"-"`: 忽略该字段（通常用于 RawLog）

### convert 标签
- `convert:"numberToString"`: 将数字类型转换为字符串
- `convert:"stringToNumber"`: 将字符串转换为数字类型
- `convert:"stringToTime"`: 将字符串转换为时间类型（支持多种格式）

### rawlog 标签
- `rawlog:"target"`: 指定该字段为 rawlog 目标字段（用于存储原始数据）
- `rawlog:"exclude"`: 该字段不会被写入到 rawlog 中

## 🏗️ 核心原理

### 1. 结构体信息缓存
DataConv 使用 `sync.Map` 缓存结构体的类型信息，避免重复反射解析，显著提升性能：

```go
// 类型信息缓存，线程安全
var typeCache sync.Map // map[reflect.Type]*structInfo
```

### 2. 智能字段映射
解码器会分析结构体的所有字段，建立字段名到字段路径的映射关系，支持：
- 普通字段映射
- 嵌套结构体字段
- 匿名字段展开
- 指针类型处理

### 3. RawLog 机制
- **灵活字段名**: 通过 `rawlog:"target"` 标签指定任意字段名作为 rawlog 存储字段
- **向后兼容**: 如果没有 `rawlog:"target"` 标签，自动使用名为 "RawLog" 的字段
- **自动收集**: 所有解码的字段（包括已知字段）默认写入 rawlog
- **排除控制**: 通过 `rawlog:"exclude"` 标签排除敏感字段
- **嵌套支持**: 嵌套结构体的数据也会写入父级 rawlog

### 4. 类型转换流程
1. 检测字段是否需要转换（`convert` 标签）
2. 先解码到临时变量
3. 应用转换函数
4. 设置到目标字段
5. 写入 RawLog（如果需要）

## 🔍 Map 格式检测

DataConv 支持检测和处理多种 MessagePack Map 格式：
- **FixedMap** (0x80-0x8f): 小型固定长度 Map
- **Map16** (0xde): 中等长度 Map  
- **Map32** (0xdf): 大型 Map

通过预读取和位置回退机制，确保正确解析嵌套结构。

## ⚡ 性能优化

1. **类型信息缓存**: 避免重复反射解析
2. **路径索引**: 快速定位嵌套字段
3. **批量处理**: 减少内存分配
4. **智能跳过**: 高效处理未知字段

## 🧪 测试覆盖

项目包含全面的测试用例，覆盖：
- ✅ 基础解码功能
- ✅ 类型转换（数字↔字符串）
- ✅ 嵌套结构体处理
- ✅ RawLog 功能
- ✅ 错误处理
- ✅ 大型 Map 解码
- ✅ 边界条件测试

运行测试：
```bash
go test -v
```

## 📚 API 参考

### 主要函数

#### MsgpackDecode
```go
func MsgpackDecode(data []byte, v interface{}) error
```
将 MessagePack 数据解码到目标结构体。

**参数:**
- `data`: MessagePack 格式的字节数据
- `v`: 目标结构体指针

**返回:**
- `error`: 解码错误，成功时为 nil

### 辅助函数

#### MapToStruct (mapstruct.go)
```go
func MapToStruct(data map[string]interface{}, target interface{}) error
```
将 map 数据转换为结构体，支持类型转换和原始数据保留。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**DataConv** - 让 MessagePack 数据转换更简单、更高效！
