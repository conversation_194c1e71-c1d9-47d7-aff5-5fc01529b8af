package dataconv

import (
	"log"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
)

func TestMapToStruct(t *testing.T) {
	Convey("Test MapToStruct functionality", t, func() {
		type NestedStruct struct {
			Field3 bool `mapstructure:"field3"`
		}

		type TestStruct struct {
			Field1 string                 `mapstructure:"field1" rawlog:"exclude"`
			Field2 int                    `mapstructure:"field2"`
			Nested NestedStruct           `mapstructure:"nested"`
			RawLog map[string]interface{} `rawlog:"target"`
			Excl   string                 `rawlog:"exclude"`
		}

		Convey("Basic map to struct conversion", func() {
			data := map[string]interface{}{
				"field1": "value1",
				"field2": 42,
				"nested": map[string]interface{}{
					"field3": true,
				},
				"excl": "should be excluded",
			}

			var result TestStruct
			err := MapToStruct(data, &result)
			So(err, ShouldBeNil)
			So(result.Field1, ShouldEqual, "value1")
			So(result.Field2, Should<PERSON><PERSON><PERSON>, 42)
			So(result.Nested.Field3, ShouldBeTrue)
			log.Printf("raw log %v", result.RawLog)
			So(result.<PERSON><PERSON><PERSON>, ShouldNot<PERSON><PERSON>ain<PERSON>ey, "field1")
			So(result.RawLog, ShouldContainKey, "excl")
			So(len(result.RawLog), ShouldEqual, 3)
		})

		Convey("Type conversion during decoding", func() {
			type ConvertStruct struct {
				IntField         int    `mapstructure:"IntField" convert:"stringToNumber"`
				NumToStringField string `mapstructure:"NumToStringField" convert:"numberToString"`
			}

			data := map[string]interface{}{
				"IntField":         "42",
				"NumToStringField": 1,
			}

			var result ConvertStruct
			err := MapToStruct(data, &result)
			So(err, ShouldBeNil)
			So(result.NumToStringField, ShouldEqual, "1")
			So(result.IntField, ShouldEqual, 42)
		})

		Convey("Nested struct conversion", func() {
			type Nested struct {
				Field1 string                 `mapstructure:"Field1"`
				Field2 int                    `mapstructure:"Field2" convert:"stringToNumber"`
				RawLog map[string]interface{} `rawlog:"target"`
			}

			type Parent struct {
				Nested1 Nested                 `mapstructure:"Nested1"`
				Nested2 *Nested                `mapstructure:"Nested2"`
				RawLog  map[string]interface{} `rawlog:"target"`
			}

			data := map[string]interface{}{
				"Nested1": map[string]interface{}{
					"S1": "s1",
					"m1": map[string]any{
						"k1": "v1",
					},
					"Field1": "value1",
					"Field2": "1", // 字符串，需要转换为数字
				},
				"Nested2": map[string]interface{}{
					"Field1": "value2",
					"Field2": 2,
				},
				"I1":   100,
				"Arr1": []int{1, 2, 3},
				"m1": map[string]any{
					"k1": 1,
				},
			}

			var result Parent
			err := MapToStruct(data, &result)
			So(err, ShouldBeNil)
			So(result.RawLog["I1"], ShouldEqual, 100)
			So(result.RawLog["m1"], ShouldContainKey, "k1")
			So(result.RawLog["Arr1"], ShouldResemble, []int{1, 2, 3})
			So(result.Nested1.RawLog["S1"], ShouldEqual, "s1")
			So(result.Nested1.RawLog["m1"], ShouldResemble, map[string]any{"k1": "v1"})
			So(result.Nested1.Field1, ShouldEqual, "value1")
			So(result.Nested1.Field2, ShouldEqual, 1)
			So(result.Nested2.Field1, ShouldEqual, "value2")
				So(result.Nested2.Field2, ShouldEqual, 2)

			So(result.Nesteds[0], ShouldResemble, Nested{Field1: "value1", Field2: 1, RawLog: map[string]interface{}{"S1": "s1", "Field1": "value1", "Field2": int64(1)}})
			So(result.Nesteds[1], ShouldResemble, Nested{Field1: "value2", Field2: 2, RawLog: map[string]interface{}{
				"Field1": "value2",
				"Field2": 2,
			}})
			So(len(result.Nesteds), ShouldEqual, 2)
			log.Printf("rawlog %v", result.Nesteds[0].RawLog)
			So(result.Nesteds[0].RawLog["S1"], ShouldEqual, "s1")
			So(result.Nesteds[1].RawLog["S1"], ShouldBeNil)

			So(result.NestedsPtr[0], ShouldResemble, &Nested{Field1: "value1", Field2: 1, RawLog: map[string]interface{}{"S1": "s1",
				"Field1": "value1",
				"Field2": int64(1),
			}})
			So(result.NestedsPtr[1], ShouldResemble, &Nested{Field1: "value2", Field2: 2, RawLog: map[string]interface{}{
				"Field1": "value2",
				"Field2": 2,
			}})
		})
	})
}
