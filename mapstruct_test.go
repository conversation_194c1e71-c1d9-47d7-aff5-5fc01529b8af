package dataconv

import (
	"log"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
)

func TestMapToStruct(t *testing.T) {
	Convey("Test MapToStruct functionality", t, func() {
		type NestedStruct struct {
			Field3 bool `mapconv:"field3"`
		}

		type TestStruct struct {
			Field1 string                 `mapconv:"field1" rawlog:"exclude"`
			Field2 int                    `mapconv:"field2"`
			Nested NestedStruct           `mapconv:"nested"`
			RawLog map[string]interface{} `rawlog:"target"`
			Excl   string                 `rawlog:"exclude"`
		}

		Convey("Basic map to struct conversion", func() {
			data := map[string]interface{}{
				"field1": "value1",
				"field2": 42,
				"nested": map[string]interface{}{
					"field3": true,
				},
				"excl": "should be excluded",
			}

			var result TestStruct
			err := MapToStruct(data, &result)
			So(err, ShouldBeNil)
			So(result.Field1, ShouldEqual, "value1")
			So(result.Field2, Should<PERSON><PERSON><PERSON>, 42)
			So(result.Nested.Field3, ShouldBeTrue)
			log.Printf("raw log %v", result.RawLog)
			So(result.<PERSON>L<PERSON>, ShouldNot<PERSON><PERSON>ain<PERSON>ey, "field1")
			So(result.RawLog, ShouldContainKey, "excl")
			So(len(result.RawLog), ShouldEqual, 3)
		})

		Convey("Type conversion during decoding", func() {
			type ConvertStruct struct {
				IntField         int       `mapconv:"IntField" convert:"stringToNumber"`
				NumToStringField string    `mapconv:"NumToStringField" convert:"numberToString"`
				TimeField        time.Time `mapconv:"TimeField" convert:"stringToTime"`
			}

			data := map[string]interface{}{
				"IntField":         "42",
				"NumToStringField": 1,
				"TimeField":        "2025-01-01",
			}

			var result ConvertStruct
			err := MapToStruct(data, &result)
			So(err, ShouldBeNil)
			So(result.NumToStringField, ShouldEqual, "1")
			So(result.IntField, ShouldEqual, 42)
			So(result.TimeField.Year(), ShouldEqual, 2025)
			So(result.TimeField.Month(), ShouldEqual, 1)
			So(result.TimeField.Day(), ShouldEqual, 1)
		})

		Convey("Nested struct conversion", func() {
			type Nested struct {
				Field1 string                 `mapconv:"Field1"`
				Field2 int                    `mapconv:"Field2" convert:"stringToNumber"`
				RawLog map[string]interface{} `rawlog:"target"`
			}

			type Parent struct {
				Nested1    Nested                 `mapconv:"Nested1"`
				Nested2    *Nested                `mapconv:"Nested2"`
				Nesteds    []Nested               `mapconv:"Nesteds"`
				NestedsPtr []*Nested              `mapconv:"NestedsPtr"`
				RawLog     map[string]interface{} `rawlog:"target"`
			}

			data := map[string]interface{}{
				"Nested1": map[string]interface{}{
					"Field1": "value1",
					"Field2": "1", // 字符串，需要转换为数字
					"S1":     "s1",
				},
				"Nested2": map[string]interface{}{
					"Field1": "value2",
					"Field2": 2,
				},
				"Nesteds": []interface{}{
					map[string]interface{}{
						"Field1": "slice_value1",
						"Field2": "10",
						"Extra":  "slice_extra1",
					},
					map[string]interface{}{
						"Field1": "slice_value2",
						"Field2": "20",
						"Extra":  "slice_extra2",
					},
				},
				"NestedsPtr": []interface{}{
					map[string]interface{}{
						"Field1": "ptr_value1",
						"Field2": "30",
						"Extra":  "ptr_extra1",
					},
					map[string]interface{}{
						"Field1": "ptr_value2",
						"Field2": "40",
						"Extra":  "ptr_extra2",
					},
				},
				"I1":   100,
				"Arr1": []int{1, 2, 3},
			}

			var result Parent
			err := MapToStruct(data, &result)
			So(err, ShouldBeNil)

			// 验证嵌套结构体
			So(result.Nested1.Field1, ShouldEqual, "value1")
			So(result.Nested1.Field2, ShouldEqual, 1) // 字符串转数字
			So(result.Nested1.RawLog["Field1"], ShouldEqual, "value1")
			So(result.Nested1.RawLog["Field2"], ShouldEqual, 1)
			So(result.Nested1.RawLog["S1"], ShouldEqual, "s1")

			// 验证指针嵌套结构体
			So(result.Nested2, ShouldNotBeNil)
			So(result.Nested2.Field1, ShouldEqual, "value2")
			So(result.Nested2.Field2, ShouldEqual, 2)

			// 验证结构体切片
			So(len(result.Nesteds), ShouldEqual, 2)
			So(result.Nesteds[0].Field1, ShouldEqual, "slice_value1")
			So(result.Nesteds[0].Field2, ShouldEqual, 10)
			So(result.Nesteds[0].RawLog["Extra"], ShouldEqual, "slice_extra1")
			So(result.Nesteds[1].Field1, ShouldEqual, "slice_value2")
			So(result.Nesteds[1].Field2, ShouldEqual, 20)

			// 验证指针结构体切片
			So(len(result.NestedsPtr), ShouldEqual, 2)
			So(result.NestedsPtr[0], ShouldNotBeNil)
			So(result.NestedsPtr[0].Field1, ShouldEqual, "ptr_value1")
			So(result.NestedsPtr[0].Field2, ShouldEqual, 30)
			So(result.NestedsPtr[1], ShouldNotBeNil)
			So(result.NestedsPtr[1].Field1, ShouldEqual, "ptr_value2")
			So(result.NestedsPtr[1].Field2, ShouldEqual, 40)

			// 验证父级 RawLog
			So(result.RawLog, ShouldNotBeNil)
			So(result.RawLog["I1"], ShouldEqual, 100)
			So(result.RawLog["Arr1"], ShouldResemble, []int{1, 2, 3})

			log.Printf("result %+v", result)
		})
	})
}
