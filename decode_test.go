package dataconv

import (
	"fmt"
	"math"
	"reflect"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/vmihailenco/msgpack/v5"
)

func TestGetStructInfo(t *testing.T) {
	Convey("Test struct info caching", t, func() {
		type TestStruct struct {
			Field1 string `msgpack:"field1"`
			Field2 int
			Nested struct {
				Field3 bool
			} `msgpack:"nested"`
			RawLog map[string]interface{}
		}

		<PERSON><PERSON>("Initial struct analysis", func() {
			info := getStructInfo(reflect.TypeOf(TestStruct{}))

			Convey("Parses all fields and tags correctly", func() {
				So(info.Fields, ShouldContainKey, "field1")
				So(info.Fields, Should<PERSON><PERSON>ain<PERSON>ey, "Field2")
				So(info.Fields, ShouldC<PERSON>ain<PERSON>ey, "nested")
				So(info.RawLogIndex, ShouldNotBeNil)
			})

			Convey("Caches the parsed struct info for subsequent use", func() {
				cached := getStructInfo(reflect.TypeOf(TestStruct{}))
				So(cached, ShouldEqual, info)
			})
		})
	})
}

func TestMsgpackDecoder(t *testing.T) {
	<PERSON><PERSON>("Test msgpack decoder functionality", t, func() {
		type TestStruct struct {
			Name   string
			Age    int
			Nested struct {
				Active bool
			}
			RawLog map[string]interface{} `msgpack:"-"`
		}

		Convey("Invalid target validation", func() {
			Convey("Returns error for non-pointer target", func() {
				var s TestStruct
				err := MsgpackDecode(nil, &s)
				So(err, ShouldNotBeNil)
			})
		})

		Convey("Basic decoding", func() {
			data, _ := msgpack.Marshal(map[string]interface{}{
				"Name": "John",
				"Age":  30,
			})

			var s TestStruct
			Convey("Decodes simple fields as expected", func() {
				err := MsgpackDecode(data, &s)
				So(err, ShouldBeNil)
				So(s.Name, ShouldEqual, "John")
				So(s.Age, ShouldEqual, 30)
			})
		})

		Convey("Nested struct decoding", func() {
			data, _ := msgpack.Marshal(map[string]interface{}{
				"Name": "John",
				"Nested": map[string]interface{}{
					"Active": true,
				},
			})

			var s TestStruct

			Convey("Correctly handles nested structure decoding", func() {
				err := MsgpackDecode(data, &s)
				So(err, ShouldBeNil)
				So(s.Nested.Active, ShouldBeTrue)
			})
		})

		Convey("Unknown field handling", func() {
			data, _ := msgpack.Marshal(map[string]interface{}{
				"Name":     "John",
				"Unknown1": "value1",
				"Unknown2": 123,
			})

			var s TestStruct

			Convey("Stores unknown fields in RawLog when present", func() {
				err := MsgpackDecode(data, &s)
				So(err, ShouldBeNil)
				So(s.RawLog, ShouldContainKey, "Unknown1")
				So(s.RawLog, ShouldContainKey, "Unknown2")
			})
		})
	})
}

func TestLargeMapDecoding(t *testing.T) {
	Convey("Test decoding large maps", t, func() {
		Convey("Decodes Map16 format with nested struct", func() {
			type Values struct {
				I2 int
				I3 int
				I4 int
				I5 int
				I6 int
				I7 int
				I8 int
				I9 int
				S1 string
				S2 string
				S3 string
				S4 string
				S5 string
				S6 string
				S7 string
				S8 string
			}
			type Nested struct {
				Values Values
			}
			type TestStruct struct {
				Name   string
				Nested Nested
			}

			data := map[string]interface{}{
				"Name": "test",
				"Nested": map[string]interface{}{
					"Values": Values{
						I2: 2,
						I3: 3,
						I4: 4,
						I5: 5,
						I6: 6,
					},
				},
			}

			encoded, _ := msgpack.Marshal(data)
			var decoded TestStruct
			err := MsgpackDecode(encoded, &decoded)

			So(err, ShouldBeNil)
			So(decoded.Nested.Values.I6, ShouldEqual, 6)
		})
	})
}

func TestDecodeStructWithRawLog(t *testing.T) {
	Convey("Test decoding with RawLog field", t, func() {
		type ChildStruct struct {
			RawLog map[string]interface{}
			I1     int
			S1     string
		}

		type TestStruct struct {
			Name   string
			C1     ChildStruct
			RawLog map[string]interface{}
		}

		data, _ := msgpack.Marshal(map[string]interface{}{
			"Name":  "John",
			"Extra": "value",
			"foo":   []int{1, 2, 3},
			"bar": map[string]any{
				"k1": 1,
				"k2": "s2",
			},
			"C1": map[string]any{
				"I1": 111,
				"I2": 222,
			},
		})

		var s TestStruct
		Convey("Preserves known fields while storing unknown fields in RawLog", func() {
			err := MsgpackDecode(data, &s)
			So(err, ShouldBeNil)
			So(s.Name, ShouldEqual, "John")
			So(s.RawLog, ShouldContainKey, "Extra")
			arr := s.RawLog["foo"].([]any)
			So(arr[0], ShouldEqual, 1)
			So(arr[2], ShouldEqual, 3)
			So(s.C1.RawLog["I2"], ShouldEqual, 222)
			So(s.RawLog["bar"], ShouldResemble, map[string]any{"k1": int8(1), "k2": "s2"})
			So(s.RawLog["C1"], ShouldResemble, s.C1)
		})
	})
}

// 测试结构体
type TestStruct struct {
	Name   string                 `msgpack:"name"`
	Age    string                 `msgpack:"age" convert:"numberToString"`
	Score  int64                  `msgpack:"score" convert:"stringToNumber"`
	Secret string                 `msgpack:"secret" rawlog:"exclude"`
	RawLog map[string]interface{} `msgpack:"-"`
}

func TestMsgpackDecodeWithConversion(t *testing.T) {
	Convey("Test msgpack decoding with type conversion", t, func() {

		Convey("Number to string", func() {
			type NumberToStringStruct struct {
				S1 string `msgpack:"s1" convert:"numberToString"`
				S2 string `msgpack:"s2" convert:"numberToString"`
				S3 string `msgpack:"s3" convert:"numberToString"`
				S4 string `msgpack:"s4" convert:"numberToString"`
				S5 int    `msgpack:"s5"`
			}

			data := map[string]any{
				"s1": 1,
				"s2": uint32(2),
				"s3": 3.14,
				"s4": 4.14,
				"s5": 5,
			}

			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)
			var result NumberToStringStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)

			So(result.S1, ShouldEqual, "1")
			So(result.S2, ShouldEqual, "2")
			So(result.S3, ShouldEqual, "3.14")
			So(result.S4, ShouldEqual, "4.14")
			So(result.S5, ShouldEqual, 5)
		})

		Convey("String to Number", func() {
			type StringToNumberStruct struct {
				N1 int     `msgpack:"n1" convert:"stringToNumber"`
				N2 int64   `msgpack:"n2" convert:"stringToNumber"`
				N3 uint64  `msgpack:"n3" convert:"stringToNumber"`
				N4 float32 `msgpack:"n4" convert:"stringToNumber"`
				N5 float64 `msgpack:"n5" convert:"stringToNumber"`
				N6 int     `msgpack:"n6"`
			}

			data := map[string]any{
				"n1": "1",
				"n2": fmt.Sprintf("%v", math.MaxInt64),
				"n3": "184467440737095",
				"n4": "4.14",
				"n5": "5.14",
				"n6": 6,
			}

			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)
			var result StringToNumberStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)

			So(result.N1, ShouldEqual, 1)
			So(result.N2, ShouldEqual, math.MaxInt64)
			So(result.N3, ShouldEqual, 184467440737095)
			So(result.N4 == 4.14, ShouldBeTrue)
			// So(result.N4, ShouldEqual, 4.14)
			So(result.N5, ShouldEqual, 5.14)
			So(result.N6, ShouldEqual, 6)
		})

		Convey("Decode with no data convert actual happend", func() {
			// 创建测试数据
			data := map[string]interface{}{
				"name":    "John",
				"age":     "25",
				"score":   95.1,           // 字符串，应该转换为数字
				"secret":  "confidential", // 应该设置到字段但不写入RawLog
				"unknown": "extra_data",   // 未知字段，应该写入RawLog
			}
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)
			So(result.Name, ShouldEqual, "John")
			So(result.Age, ShouldEqual, "25")
			So(result.Score, ShouldEqual, 95)
			So(result.Secret, ShouldEqual, "confidential")

			
			So(result.RawLog, ShouldNotBeNil)
			So(result.RawLog["name"], ShouldEqual, "John")
			So(result.RawLog["age"], ShouldEqual, "25")
			So(result.RawLog["score"], ShouldEqual, 95.1)
			So(result.RawLog["unknown"], ShouldEqual, "extra_data")
			So(result.RawLog, ShouldNotContainKey, "secret")
		})

		Convey("Decode success case 1", func() {
			// 创建测试数据
			data := map[string]interface{}{
				"name":    "John",
				"age":     25,             // 数字，应该转换为字符串
				"score":   "95",           // 字符串，应该转换为数字
				"secret":  "confidential", // 应该设置到字段但不写入RawLog
				"unknown": "extra_data",   // 未知字段，应该写入RawLog
			}
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "John")
			So(result.Age, ShouldEqual, "25")
			So(result.Score, ShouldEqual, 95)
			So(result.Secret, ShouldEqual, "confidential")

			So(result.RawLog, ShouldNotBeNil)
			So(result.RawLog["name"], ShouldEqual, "John")
			So(result.RawLog["age"], ShouldEqual, "25")
			So(result.RawLog["score"], ShouldEqual, int64(95))
			So(result.RawLog["unknown"], ShouldEqual, "extra_data")
			So(result.RawLog, ShouldNotContainKey, "secret")
		})

		Convey("Decode success case 2", func() {
			type TestStruct2 struct {
				Name   string                 `msgpack:"name"`
				Age    string                 `msgpack:"age" convert:"numberToString"`
				Score  float64                `msgpack:"score" convert:"stringToNumber"`
				Secret string                 `msgpack:"secret" rawlog:"exclude"`
				RawLog map[string]interface{} `msgpack:"-"`
			}

			// 创建测试数据
			data := map[string]interface{}{
				"name":    "John",
				"age":     25,             // 数字，应该转换为字符串
				"score":   "95.01",        // 字符串，应该转换为数字
				"secret":  "confidential", // 应该设置到字段但不写入RawLog
				"unknown": "extra_data",   // 未知字段，应该写入RawLog
			}
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct2
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "John")
			So(result.Age, ShouldEqual, "25")
			So(result.Score, ShouldEqual, 95.01)
			So(result.Secret, ShouldEqual, "confidential")

			So(result.RawLog, ShouldNotBeNil)
			So(result.RawLog["name"], ShouldEqual, "John")
			So(result.RawLog["age"], ShouldEqual, "25")
			So(result.RawLog["score"], ShouldEqual, 95.01)
			So(result.RawLog["unknown"], ShouldEqual, "extra_data")
			So(result.RawLog, ShouldNotContainKey, "secret")
		})

		Convey("Decode success case 3", func() {
			type AnonymousStruct struct {
				Name   string                 `msgpack:"name"`
				Age    string                 `msgpack:"age" convert:"numberToString"`
				Score  float64                `msgpack:"score" convert:"stringToNumber"`
				RawLog map[string]interface{} `msgpack:"-"`
			}
			type TestStruct2 struct {
				AnonymousStruct
				A1     AnonymousStruct `msgpack:"a1"`
				Secret string          `msgpack:"secret" rawlog:"exclude"`
			}

			// 创建测试数据
			data := map[string]interface{}{
				"a1": AnonymousStruct{
					Name:  "a1",
					Age:   "11",
					Score: 11.1,
					RawLog: map[string]interface{}{
						"name": "a1",
						"age":  "11",
					},
				},
				"name":    "John",
				"age":     25,             // 数字，应该转换为字符串
				"score":   "95.01",        // 字符串，应该转换为数字
				"secret":  "confidential", // 应该设置到字段但不写入RawLog
				"unknown": "extra_data",   // 未知字段，应该写入RawLog
			}
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct2
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "John")
			So(result.Age, ShouldEqual, "25")
			So(result.Score, ShouldEqual, 95.01)
			So(result.Secret, ShouldEqual, "confidential")

			So(result.RawLog, ShouldNotBeNil)
			So(result.RawLog["name"], ShouldEqual, "John")
			So(result.RawLog["age"], ShouldEqual, "25")
			So(result.RawLog["score"], ShouldEqual, 95.01)
			So(result.RawLog["unknown"], ShouldEqual, "extra_data")
			So(result.RawLog, ShouldNotContainKey, "secret")
			So(result.RawLog["a1"], ShouldResemble, result.A1)
			So(result.A1, ShouldResemble, AnonymousStruct{
				Name:  "a1",
				Age:   "11",
				Score: 11.1,
				RawLog: map[string]interface{}{
					"name":  "a1",
					"age":   "11",
					"score": 11.1,
				},
			})
		})

		Convey("Failed for string to number convert error", func() {
			// 创建测试数据
			data := map[string]interface{}{
				"name":    "John",
				"age":     "25",
				"score":   []any{1, 2, 3}, // 字符串，应该转换为数字
				"secret":  "confidential", // 应该设置到字段但不写入RawLog
				"unknown": "extra_data",   // 未知字段，应该写入RawLog
			}
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldNotBeNil)
		})

		Convey("Failed for string to number convert error 2", func() {
			// 创建测试数据
			data := map[string]interface{}{
				"name":    "John",
				"age":     "25",
				"score":   "abcd",         // 字符串，应该转换为数字
				"secret":  "confidential", // 应该设置到字段但不写入RawLog
				"unknown": "extra_data",   // 未知字段，应该写入RawLog
			}
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldNotBeNil)
		})

		Convey("Failed for number to string convert error", func() {
			// 创建测试数据
			data := map[string]interface{}{
				"name":    "John",
				"age":     map[string]any{"a": 1},
				"score":   "111",          // 字符串，应该转换为数字
				"secret":  "confidential", // 应该设置到字段但不写入RawLog
				"unknown": "extra_data",   // 未知字段，应该写入RawLog
			}
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldNotBeNil)
		})

		Convey("Failed commn msgpack error", func() {
			// 创建测试数据
			data := map[string]interface{}{
				"name": 111,
			}
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldNotBeNil)
		})

		Convey("Failed invalid convert tag", func() {
			type InvalidTagStruct struct {
				S1 int `msgpack:"s1" convert:"aTob"`
			}

			// 创建测试数据
			data := map[string]interface{}{
				"s1": 111,
			}
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result InvalidTagStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldNotBeNil)
		})

	})
}

func TestMsgpackDecodeWithoutConversion(t *testing.T) {
	Convey("Test msgpack decoding without type conversion", t, func() {
		type ChildStruct struct {
			Name   string                 `msgpack:"name"`
			Age    int                    `msgpack:"age"`
			Addr   string                 `msgpack:"addr" rawlog:"exclude"`
			RawLog map[string]interface{} `msgpack:"-"`
		}

		type SimpleStruct struct {
			Name   string                 `msgpack:"name"`
			Age    int                    `msgpack:"age"`
			RawLog map[string]interface{} `msgpack:"-"`
			C1     ChildStruct            `msgpack:"c1"`
			C2     *ChildStruct           `msgpack:"c2"`
			C3     ChildStruct            `rawlog:"exclude" msgpack:"c3"`
		}

		data := map[string]interface{}{
			"name":    "Alice",
			"age":     30,
			"unknown": "extra",
			"m":       map[string]any{"k1": 2},
			"c1": map[string]any{
				"addr": "a1",
				"name": "c1",
				"age":  1,
			},
			"c2": map[string]any{
				"name": "c2",
				"age":  2,
			},
			"c3": map[string]any{
				"name": "c3",
				"age":  3,
			},
		}

		Convey("Success", func() {
			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result SimpleStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "Alice")
			So(result.Age, ShouldEqual, 30)
			So(result.RawLog["name"], ShouldEqual, "Alice")
			So(result.RawLog["age"], ShouldEqual, 30)
			So(result.RawLog["unknown"], ShouldEqual, "extra")
			So(result.RawLog["m"], ShouldResemble, map[string]any{"k1": int8(2)})

			So(result.C1, ShouldResemble, ChildStruct{
				Name: "c1",
				Age:  1,
				Addr: "a1",
				RawLog: map[string]interface{}{
					"name": "c1",
					"age":  1,
				},
			})

			So(result.C2, ShouldResemble, &ChildStruct{
				Name: "c2",
				Age:  2,
				RawLog: map[string]interface{}{
					"name": "c2",
					"age":  2,
				},
			})

			So(result.C3, ShouldResemble, ChildStruct{
				Name: "c3",
				Age:  3,
				RawLog: map[string]interface{}{
					"name": "c3",
					"age":  3,
				},
			})

			So(result.RawLog["c1"], ShouldResemble, result.C1)
			So(result.RawLog["c2"], ShouldResemble, *result.C2)
			So(result.RawLog["c3"], ShouldBeNil)
		})
	})
}

func TestMapToStructRawLogConsistency(t *testing.T) {
	Convey("Test MapToStruct rawlog consistency with MsgpackDecode", t, func() {
		type ChildStruct struct {
			Name   string                 `mapstructure:"name"`
			Age    int                    `mapstructure:"age"`
			Addr   string                 `mapstructure:"addr" rawlog:"exclude"`
			RawLog map[string]interface{} `mapstructure:"-"`
		}

		type TestStruct struct {
			Name   string                 `mapstructure:"name"`
			Age    string                 `mapstructure:"age" convert:"numberToString"`
			Score  int64                  `mapstructure:"score" convert:"stringToNumber"`
			Secret string                 `mapstructure:"secret" rawlog:"exclude"`
			Child  ChildStruct            `mapstructure:"child"`
			RawLog map[string]interface{} `mapstructure:"-"`
		}

		data := map[string]interface{}{
			"name":    "John",
			"age":     25,             // 数字，应该转换为字符串
			"score":   "95",           // 字符串，应该转换为数字
			"secret":  "confidential", // 应该设置到字段但不写入RawLog
			"unknown": "extra_data",   // 未知字段，应该写入RawLog
			"child": map[string]interface{}{
				"name": "child1",
				"age":  10,
				"addr": "address1", // 应该设置到字段但不写入子结构的RawLog
				"extra": "child_extra", // 未知字段，应该写入子结构的RawLog
			},
		}

		Convey("MapToStruct should have same rawlog behavior as MsgpackDecode", func() {
			var result TestStruct
			err := MapToStruct(data, &result)
			So(err, ShouldBeNil)

			// 验证字段值
			So(result.Name, ShouldEqual, "John")
			So(result.Age, ShouldEqual, "25")
			So(result.Score, ShouldEqual, 95)
			So(result.Secret, ShouldEqual, "confidential")
			So(result.Child.Name, ShouldEqual, "child1")
			So(result.Child.Age, ShouldEqual, 10)
			So(result.Child.Addr, ShouldEqual, "address1")

			// 验证 RawLog 行为与 decode.go 一致
			So(result.RawLog, ShouldNotBeNil)
			So(result.RawLog["name"], ShouldEqual, "John")
			So(result.RawLog["age"], ShouldEqual, "25")      // 转换后的值
			So(result.RawLog["score"], ShouldEqual, int64(95)) // 转换后的值
			So(result.RawLog["unknown"], ShouldEqual, "extra_data")
			So(result.RawLog, ShouldNotContainKey, "secret") // 排除字段

			// 验证嵌套结构体也写入父级 RawLog
			So(result.RawLog["child"], ShouldResemble, result.Child)

			// 验证子结构的 RawLog
			So(result.Child.RawLog, ShouldNotBeNil)
			So(result.Child.RawLog["name"], ShouldEqual, "child1")
			So(result.Child.RawLog["age"], ShouldEqual, 10)
			So(result.Child.RawLog["extra"], ShouldEqual, "child_extra")
			So(result.Child.RawLog, ShouldNotContainKey, "addr") // 排除字段
		})
	})
}

func TestRawLogTargetTag(t *testing.T) {
	Convey("Test rawlog target tag functionality", t, func() {

		Convey("MsgpackDecode with rawlog:target tag", func() {
			type TestStruct struct {
				Name     string                 `msgpack:"name"`
				Age      int                    `msgpack:"age"`
				Secret   string                 `msgpack:"secret" rawlog:"exclude"`
				MyRawLog map[string]interface{} `msgpack:"-" rawlog:"target"`
			}

			data := map[string]interface{}{
				"name":    "Alice",
				"age":     25,
				"secret":  "confidential",
				"unknown": "extra_data",
			}

			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "Alice")
			So(result.Age, ShouldEqual, 25)
			So(result.Secret, ShouldEqual, "confidential")

			// 验证自定义名称的 rawlog 字段
			So(result.MyRawLog, ShouldNotBeNil)
			So(result.MyRawLog["name"], ShouldEqual, "Alice")
			So(result.MyRawLog["age"], ShouldEqual, 25)
			So(result.MyRawLog["unknown"], ShouldEqual, "extra_data")
			So(result.MyRawLog, ShouldNotContainKey, "secret") // 排除字段
		})

		Convey("MapToStruct with rawlog:target tag", func() {
			type TestStruct struct {
				Name     string                 `mapstructure:"name"`
				Age      string                 `mapstructure:"age" convert:"numberToString"`
				Secret   string                 `mapstructure:"secret" rawlog:"exclude"`
				MyRawLog map[string]interface{} `mapstructure:"-" rawlog:"target"`
			}

			data := map[string]interface{}{
				"name":    "Bob",
				"age":     30,
				"secret":  "confidential",
				"unknown": "extra_data",
			}

			var result TestStruct
			err := MapToStruct(data, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "Bob")
			So(result.Age, ShouldEqual, "30")
			So(result.Secret, ShouldEqual, "confidential")

			// 验证自定义名称的 rawlog 字段
			So(result.MyRawLog, ShouldNotBeNil)
			So(result.MyRawLog["name"], ShouldEqual, "Bob")
			So(result.MyRawLog["age"], ShouldEqual, "30") // 转换后的值
			So(result.MyRawLog["unknown"], ShouldEqual, "extra_data")
			So(result.MyRawLog, ShouldNotContainKey, "secret") // 排除字段
		})

		Convey("Backward compatibility - RawLog field name without tag", func() {
			type TestStruct struct {
				Name   string                 `msgpack:"name"`
				Age    int                    `msgpack:"age"`
				Secret string                 `msgpack:"secret" rawlog:"exclude"`
				RawLog map[string]interface{} `msgpack:"-"`
			}

			data := map[string]interface{}{
				"name":    "Charlie",
				"age":     35,
				"secret":  "confidential",
				"unknown": "extra_data",
			}

			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "Charlie")
			So(result.Age, ShouldEqual, 35)
			So(result.Secret, ShouldEqual, "confidential")

			// 验证向后兼容性 - RawLog 字段仍然工作
			So(result.RawLog, ShouldNotBeNil)
			So(result.RawLog["name"], ShouldEqual, "Charlie")
			So(result.RawLog["age"], ShouldEqual, 35)
			So(result.RawLog["unknown"], ShouldEqual, "extra_data")
			So(result.RawLog, ShouldNotContainKey, "secret") // 排除字段
		})

		Convey("Priority - rawlog:target takes precedence over RawLog field name", func() {
			type TestStruct struct {
				Name     string                 `msgpack:"name"`
				Age      int                    `msgpack:"age"`
				RawLog   map[string]interface{} `msgpack:"-"`                    // 这个不会被使用
				MyRawLog map[string]interface{} `msgpack:"-" rawlog:"target"`   // 这个会被使用
			}

			data := map[string]interface{}{
				"name":    "David",
				"age":     40,
				"unknown": "extra_data",
			}

			msgpackData, err := msgpack.Marshal(data)
			So(err, ShouldBeNil)

			var result TestStruct
			err = MsgpackDecode(msgpackData, &result)
			So(err, ShouldBeNil)

			So(result.Name, ShouldEqual, "David")
			So(result.Age, ShouldEqual, 40)

			// 验证优先级 - rawlog:target 优先于 RawLog 字段名
			So(result.MyRawLog, ShouldNotBeNil)
			So(result.MyRawLog["name"], ShouldEqual, "David")
			So(result.MyRawLog["age"], ShouldEqual, 40)
			So(result.MyRawLog["unknown"], ShouldEqual, "extra_data")

			// RawLog 字段应该为空
			So(result.RawLog, ShouldBeNil)
		})
	})
}

func TestMapToStructDebug(t *testing.T) {
	Convey("Debug MapToStruct", t, func() {
		type TestStruct struct {
			Name string `mapstructure:"name"`
			Age  string `mapstructure:"age" convert:"numberToString"`
		}

		data := map[string]interface{}{
			"name": "John",
			"age":  25,
		}

		var result TestStruct
		err := MapToStruct(data, &result)
		So(err, ShouldBeNil)
		So(result.Name, ShouldEqual, "John")
		So(result.Age, ShouldEqual, "25")
	})
}
