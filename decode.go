// Package dataconv 提供msgpack解码功能，支持结构体转换和rawlog处理
package dataconv

import (
	"bytes"
	"fmt"
	"io"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/vmihailenco/msgpack/v5"
	"github.com/vmihailenco/msgpack/v5/msgpcode"
)

// 类型信息缓存
var typeCache sync.Map // map[reflect.Type]*structInfo

// 结构体字段信息
type fieldInfo struct {
	Index         int          // 字段在结构体中的索引
	Name          string       // 字段名
	IsStruct      bool         // 是否是结构体
	IsPtr         bool         // 是否是指针
	IsSlice       bool         // 是否是切片
	ElemType      reflect.Type // 如果是指针，指向的类型
	SliceElemType reflect.Type // 如果是切片，元素的类型
	ConvertType   string       // 转换类型：numberToString, stringToNumber, stringToTime
	ExcludeRawLog bool         // 是否从 rawlog 中排除
}

// 结构体类型信息
type structInfo struct {
	Fields      map[string][]int      // 字段名到字段路径的映射
	FieldInfos  map[string]*fieldInfo // 字段名到字段信息的映射
	RawLogIndex []int                 // RawLog字段的路径，如果存在
}

// MsgpackDecode 解码msgpack数据到目标结构体
func MsgpackDecode(data []byte, v interface{}) error {
	decoder := newMsgpackDecoder(bytes.NewReader(data))
	return decoder.decode(v)
}

// 获取结构体类型信息，优先从缓存读取
func getStructInfo(t reflect.Type) *structInfo {
	if cached, ok := typeCache.Load(t); ok {
		return cached.(*structInfo)
	}

	info := &structInfo{
		Fields:     make(map[string][]int),
		FieldInfos: make(map[string]*fieldInfo),
	}

	var analyzeStruct func(reflect.Type, []int, string)
	analyzeStruct = func(t reflect.Type, index []int, prefix string) {
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			fieldIndex := append(append([]int{}, index...), i)

			if field.PkgPath != "" && !field.Anonymous {
				continue
			}

			// 解析 convert 和 rawlog 标签
			convertTag := field.Tag.Get("convert")
			rawlogTag := field.Tag.Get("rawlog")

			// 检查是否是 rawlog 目标字段
			if rawlogTag == "target" && field.Type.Kind() == reflect.Map {
				info.RawLogIndex = fieldIndex
				continue // rawlog 目标字段不需要进一步处理
			} else if info.RawLogIndex == nil && field.Name == "RawLog" && field.Type.Kind() == reflect.Map {
				// 向后兼容：如果没有标记 rawlog:"target" 的字段，则使用名为 "RawLog" 的字段
				info.RawLogIndex = fieldIndex
				continue // RawLog 字段不需要进一步处理
			}

			tag := field.Tag.Get("msgpack")
			if tag == "-" {
				continue
			}

			fieldName := field.Name
			if tag != "" {
				if idx := strings.IndexByte(tag, ','); idx > 0 {
					fieldName = tag[:idx]
				} else if tag != "" {
					fieldName = tag
				}
			}

			// 创建字段信息
			fInfo := &fieldInfo{
				Index:         i,
				Name:          fieldName,
				ConvertType:   convertTag,
				ExcludeRawLog: rawlogTag == "exclude",
			}

			if field.Anonymous {
				fieldType := field.Type
				if fieldType.Kind() == reflect.Ptr {
					fieldType = fieldType.Elem()
				}

				if fieldType.Kind() == reflect.Struct {
					analyzeStruct(fieldType, fieldIndex, prefix)
					continue
				}
			}

			name := fieldName
			if prefix != "" {
				name = prefix + "." + name
			}



			// 设置字段类型信息
			if field.Type.Kind() == reflect.Struct {
				fInfo.IsStruct = true
			} else if field.Type.Kind() == reflect.Ptr {
				fInfo.IsPtr = true
				fInfo.ElemType = field.Type.Elem()
				if fInfo.ElemType.Kind() == reflect.Struct {
					fInfo.IsStruct = true
				}
			}

			info.Fields[fieldName] = fieldIndex
			info.FieldInfos[fieldName] = fInfo
		}
	}

	analyzeStruct(t, nil, "")
	typeCache.Store(t, info)

	return info
}

// msgpackDecoder 解码器，封装msgpack解码逻辑
type msgpackDecoder struct {
	decoder *msgpack.Decoder // 底层msgpack解码器
}

// newMsgpackDecoder 创建新的解码器
func newMsgpackDecoder(r io.Reader) *msgpackDecoder {
	return &msgpackDecoder{
		decoder: msgpack.NewDecoder(r),
	}
}

// decode 解码msgpack数据到目标结构体
// 参数v必须是一个非空指针，指向要解码的目标结构体
// 支持嵌套结构体解码和未知字段存储到RawLog字段
func (d *msgpackDecoder) decode(v interface{}) error {
	rv := reflect.ValueOf(v)
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		return fmt.Errorf("target must be a non-nil pointer")
	}

	rv = rv.Elem()
	if rv.Kind() != reflect.Struct {
		return d.decoder.Decode(v)
	}

	mapLen, err := d.decoder.DecodeMapLen()
	if err != nil {
		return err
	}

	return d.decodeStructWithRawLog(rv, mapLen)
}

// decodeStructWithRawLog 解码带有RawLog字段的结构体
func (d *msgpackDecoder) decodeStructWithRawLog(v reflect.Value, mapLen int) error {
	// 获取结构体元信息
	t := v.Type()
	info := getStructInfo(t)


		// 初始化RawLog
	var rawLog reflect.Value
	if info.RawLogIndex != nil {
		rawLog = getFieldByPath(v, info.RawLogIndex)
		if rawLog.IsNil() {
			rawLog.Set(reflect.MakeMap(rawLog.Type()))
		}
	}

	// 遍历处理每个键值对
	for i := 0; i < mapLen; i++ {
		key, err := d.decoder.DecodeString()
		if err != nil {
			return err
		}

		// 处理已知字段
		if fieldPath, ok := info.Fields[key]; ok {
			if err := d.decodeKnownField(v, key, fieldPath, info, rawLog); err != nil {
				return err
			}
		} else {
			// 处理未知字段
			if err := d.decodeUnknownField(key, rawLog); err != nil {
				return err
			}
		}
	}

	return nil
}

// decodeKnownField 处理结构体中已定义的字段
func (d *msgpackDecoder) decodeKnownField(v reflect.Value, key string, fieldPath []int, info *structInfo, rawLog reflect.Value) error {
	field := getFieldByPath(v, fieldPath)
	fieldInfo := info.FieldInfos[key]

	// 处理嵌套结构体
	if field.Kind() == reflect.Struct || (field.Kind() == reflect.Ptr && field.Type().Elem().Kind() == reflect.Struct) {
		isMap, nestedMapLen, err := d.isMapNext()
		if err != nil {
			return err
		}

		if isMap {
			_, err := d.decoder.DecodeMapLen()
			if err != nil {
				return err
			}

			// 处理指针类型
			if field.Kind() == reflect.Ptr {
				if field.IsNil() {
					field.Set(reflect.New(field.Type().Elem()))
				}
				field = field.Elem()
			}

			if err := d.decodeStructWithRawLog(field, nestedMapLen); err != nil {
				return err
			}

			// 嵌套结构体也需要写入 rawLog（除非明确排除）
			if rawLog.IsValid() && !rawLog.IsNil() && !fieldInfo.ExcludeRawLog {
				rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(field.Interface()))
			}

			return nil
		}
	}

	// 处理普通字段
	return d.decodeSimpleField(field, fieldInfo, key, rawLog)
}

// decodeSimpleField 处理简单类型字段（非嵌套结构体）
func (d *msgpackDecoder) decodeSimpleField(field reflect.Value, fieldInfo *fieldInfo, key string, rawLog reflect.Value) error {
	needsConversion := fieldInfo.ConvertType != ""
	needsRawLog := rawLog.IsValid() && !rawLog.IsNil() && !fieldInfo.ExcludeRawLog

	if needsConversion {
		// 需要转换时，先解码到临时变量进行转换
		var originalValue interface{}
		if err := d.decoder.Decode(&originalValue); err != nil {
			return err
		}

		// 应用数据转换
		convertedValue, err := convertValue(originalValue, fieldInfo.ConvertType)
		if err != nil {
			return err
		}

		// 设置字段值
		if err := setFieldValue(field, convertedValue); err != nil {
			return err
		}

		// 写入 rawLog
		if needsRawLog {
			rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(convertedValue))
		}
	} else {
		// 不需要转换时，直接解码到目标字段
		if err := d.decoder.DecodeValue(field); err != nil {
			return err
		}

		// 如果需要写入 rawLog，从字段中读取值
		if needsRawLog {
			rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(field.Interface()))
		}
	}

	return nil
}

// decodeUnknownField 处理结构体中未定义的字段，存入RawLog
func (d *msgpackDecoder) decodeUnknownField(key string, rawLog reflect.Value) error {
	if rawLog.IsValid() && !rawLog.IsNil() {
		var value interface{}
		if err := d.decoder.Decode(&value); err != nil {
			return err
		}
		rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(value))
	} else {
		// 没有RawLog字段，跳过未知字段
		if err := d.decoder.Skip(); err != nil {
			return err
		}
	}
	return nil
}

// setFieldValue 设置字段值，处理类型转换
func setFieldValue(field reflect.Value, value interface{}) error {
	if !field.CanSet() {
		return fmt.Errorf("field cannot be set")
	}

	valueReflect := reflect.ValueOf(value)
	fieldType := field.Type()

	// 如果类型完全匹配，直接设置
	if valueReflect.Type() == fieldType {
		field.Set(valueReflect)
		return nil
	}

	// 尝试直接类型转换
	if valueReflect.Type().ConvertibleTo(fieldType) {
		field.Set(valueReflect.Convert(fieldType))
		return nil
	}

	// 对于无法直接转换的情况，返回错误
	return fmt.Errorf("cannot convert %v to %v", valueReflect.Type(), fieldType)
}


func getFieldByPath(v reflect.Value, path []int) reflect.Value {
	for _, i := range path {
		if v.Kind() == reflect.Ptr {
			if v.IsNil() {
				v.Set(reflect.New(v.Type().Elem()))
			}
			v = v.Elem()
		}
		v = v.Field(i)
	}
	return v
}

// isMapNext 检测下一个要解码的是否是map类型
// 返回值:
// bool: 是否是map
// int: map的长度
// error: 错误信息
// 支持检测FixedMap/Map16/Map32三种msgpack map格式
func (d *msgpackDecoder) isMapNext() (bool, int, error) {
	r, ok := d.decoder.Buffered().(*bytes.Reader)
	if !ok {
		return false, 0, fmt.Errorf("buffered reader is not a *bytes.Reader")
	}

	code, err := d.decoder.PeekCode()
	if err != nil {
		return false, 0, err
	}

	var mapLen int
	isMap := false

	if code >= msgpcode.FixedMapLow && code <= msgpcode.FixedMapHigh {
		mapLen = int(code & msgpcode.FixedMapMask)
		isMap = true
	} else if code == msgpcode.Map16 || code == msgpcode.Map32 {
		isMap = true

		pos, _ := r.Seek(0, io.SeekCurrent)
		mapLen, err = d.decoder.DecodeMapLen()
		if err != nil {
			return false, 0, err
		}

		_, err = r.Seek(pos, io.SeekStart)
		if err != nil {
			return false, 0, err
		}
	}

	return isMap, mapLen, nil
}

// convertValue 根据转换类型转换值
func convertValue(value interface{}, convertType string) (interface{}, error) {
	switch convertType {
	case "numberToString":
		return convertNumberToString(value)
	case "stringToNumber":
		return convertStringToNumber(value)
	case "stringToTime":
		return convertStringToTime(value)
	default:
		return value, fmt.Errorf("invalid convert type %s", convertType)
	}
}

// convertNumberToString 将数字转换为字符串
func convertNumberToString(value interface{}) (interface{}, error) {
	switch v := value.(type) {
	case int, int8, int16, int32, int64:
		return strconv.FormatInt(reflect.ValueOf(v).Int(), 10), nil
	case uint, uint8, uint16, uint32, uint64:
		return strconv.FormatUint(reflect.ValueOf(v).Uint(), 10), nil
	case float32, float64:
		return strconv.FormatFloat(reflect.ValueOf(v).Float(), 'f', -1, 64), nil
	default:
		return value, nil // 如果不是数字类型，返回原值
	}
}

// convertStringToNumber 将字符串转换为数字
func convertStringToNumber(value interface{}) (interface{}, error) {
	str, ok := value.(string)
	if !ok {
		return value, nil // 如果不是字符串类型，返回原值
	}

	// 尝试转换为整数
	if intVal, err := strconv.ParseInt(str, 10, 64); err == nil {
		return intVal, nil
	}

	// 尝试转换为浮点数
	if floatVal, err := strconv.ParseFloat(str, 64); err == nil {
		return floatVal, nil
	}

	return value, nil // 如果转换失败，返回原值
}

// convertStringToTime 将字符串转换为时间
func convertStringToTime(value interface{}) (interface{}, error) {
	str, ok := value.(string)
	if !ok {
		return value, nil // 如果不是字符串类型，返回原值
	}

	// 支持多种时间格式
	timeFormats := []string{
		"2006-01-02",
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02T15:04:05-07:00",
		time.RFC3339,
		time.RFC3339Nano,
	}

	for _, format := range timeFormats {
		if t, err := time.Parse(format, str); err == nil {
			return t, nil
		}
	}

	return value, nil // 如果转换失败，返回原值
}
