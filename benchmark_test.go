package dataconv

import (
	"testing"

	"github.com/vmihailenco/msgpack/v5"
)

// 基准测试结构体
type BenchmarkStruct struct {
	Name     string                 `msgpack:"name" mapstructure:"name"`
	Age      string                 `msgpack:"age" mapstructure:"age" convert:"numberToString"`
	Score    int64                  `msgpack:"score" mapstructure:"score" convert:"stringToNumber"`
	Email    string                 `msgpack:"email" mapstructure:"email"`
	Active   bool                   `msgpack:"active" mapstructure:"active"`
	Balance  float64                `msgpack:"balance" mapstructure:"balance"`
	Secret   string                 `msgpack:"secret" mapstructure:"secret" rawlog:"exclude"`
	RawLog   map[string]interface{} `msgpack:"-" mapstructure:"-" rawlog:"target"`
	Nested   NestedStruct           `msgpack:"nested" mapstructure:"nested"`
}

type NestedStruct struct {
	City     string                 `msgpack:"city" mapstructure:"city"`
	Country  string                 `msgpack:"country" mapstructure:"country"`
	ZipCode  string                 `msgpack:"zip_code" mapstructure:"zip_code" convert:"numberToString"`
	RawLog   map[string]interface{} `msgpack:"-" mapstructure:"-" rawlog:"target"`
}

// 测试数据
var benchmarkData = map[string]interface{}{
	"name":     "John Doe",
	"age":      30,
	"score":    "95",
	"email":    "<EMAIL>",
	"active":   true,
	"balance":  1234.56,
	"secret":   "confidential",
	"unknown1": "extra_data_1",
	"unknown2": "extra_data_2",
	"nested": map[string]interface{}{
		"city":     "New York",
		"country":  "USA",
		"zip_code": 10001,
		"extra":    "nested_extra",
	},
}

var benchmarkMsgpackData []byte

func init() {
	var err error
	benchmarkMsgpackData, err = msgpack.Marshal(benchmarkData)
	if err != nil {
		panic(err)
	}
}

// 基准测试：MsgpackDecode
func BenchmarkMsgpackDecode(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result BenchmarkStruct
		if err := MsgpackDecode(benchmarkMsgpackData, &result); err != nil {
			b.Fatal(err)
		}
	}
}

// 基准测试：MapToStruct（优化后）
func BenchmarkMapToStruct(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result BenchmarkStruct
		if err := MapToStruct(benchmarkData, &result); err != nil {
			b.Fatal(err)
		}
	}
}

// 内存分配基准测试：MsgpackDecode
func BenchmarkMsgpackDecodeAllocs(b *testing.B) {
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result BenchmarkStruct
		if err := MsgpackDecode(benchmarkMsgpackData, &result); err != nil {
			b.Fatal(err)
		}
	}
}

// 内存分配基准测试：MapToStruct（优化后）
func BenchmarkMapToStructAllocs(b *testing.B) {
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result BenchmarkStruct
		if err := MapToStruct(benchmarkData, &result); err != nil {
			b.Fatal(err)
		}
	}
}

// 复杂嵌套结构基准测试
type ComplexStruct struct {
	Level1 Level1Struct `msgpack:"level1" mapstructure:"level1"`
	RawLog map[string]interface{} `msgpack:"-" mapstructure:"-" rawlog:"target"`
}

type Level1Struct struct {
	Field1 string       `msgpack:"field1" mapstructure:"field1"`
	Level2 Level2Struct `msgpack:"level2" mapstructure:"level2"`
	RawLog map[string]interface{} `msgpack:"-" mapstructure:"-" rawlog:"target"`
}

type Level2Struct struct {
	Field2 string                 `msgpack:"field2" mapstructure:"field2"`
	Field3 int                    `msgpack:"field3" mapstructure:"field3"`
	RawLog map[string]interface{} `msgpack:"-" mapstructure:"-" rawlog:"target"`
}

var complexData = map[string]interface{}{
	"level1": map[string]interface{}{
		"field1": "value1",
		"level2": map[string]interface{}{
			"field2": "value2",
			"field3": 123,
			"extra2": "nested_extra2",
		},
		"extra1": "nested_extra1",
	},
	"extra0": "root_extra",
}

var complexMsgpackData []byte

func init() {
	var err error
	complexMsgpackData, err = msgpack.Marshal(complexData)
	if err != nil {
		panic(err)
	}
}

// 复杂嵌套结构基准测试：MsgpackDecode
func BenchmarkComplexMsgpackDecode(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComplexStruct
		if err := MsgpackDecode(complexMsgpackData, &result); err != nil {
			b.Fatal(err)
		}
	}
}

// 复杂嵌套结构基准测试：MapToStruct
func BenchmarkComplexMapToStruct(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result ComplexStruct
		if err := MapToStruct(complexData, &result); err != nil {
			b.Fatal(err)
		}
	}
}
