package dataconv

import (
	"testing"
	"time"

	"github.com/mitchellh/mapstructure"
	"github.com/vmihailenco/msgpack/v5"
)

// 基础测试结构体
type BasicStruct struct {
	Field1  string    `msgpack:"field1"`
	Field2  int       `msgpack:"field2"`
	Field3  float64   `msgpack:"field3"`
	Field4  bool      `msgpack:"field4"`
	Field5  time.Time `msgpack:"field5"`
	Field6  string    `msgpack:"field6"`
	Field7  int       `msgpack:"field7"`
	Field8  float64   `msgpack:"field8"`
	Field9  bool      `msgpack:"field9"`
	Field10 time.Time `msgpack:"field10"`
}

// 原生msgpack解码使用的结构体
type NativeComplexStruct struct {
	BasicField BasicStruct `msgpack:"basic_field"`
	Nested1    struct {
		Field1  int       `msgpack:"field1"`
		Field2  string    `msgpack:"field2"`
		Field4  float64   `msgpack:"field4"`
		Field5  time.Time `msgpack:"field5"`
		Nested2 struct {
			Field1 float64 `msgpack:"field1"`
			Field2 int     `msgpack:"field2"`
			Field3 bool    `msgpack:"field3"`
			Field4 string  `msgpack:"field4"`
		} `msgpack:"nested2"`
	} `msgpack:"nested1"`
}

// decode内部解码使用的结构体
type DecodeComplexStruct struct {
	BasicField BasicStruct `msgpack:"basic_field"`
	Nested1    struct {
		Field1  int       `msgpack:"field1" convert:"stringToNumber"`
		Field2  string    `msgpack:"field2"`
		Field4  float64   `msgpack:"field4"`
		Field5  time.Time `msgpack:"field5"`
		Nested2 struct {
			Field1 float64 `msgpack:"field1" convert:"stringToNumber"`
			Field2 int     `msgpack:"field2"`
			Field3 bool    `msgpack:"field3"`
			Field4 string  `msgpack:"field4"`
		} `msgpack:"nested2"`
	} `msgpack:"nested1"`
}

// decode内部解码使用的结构体
type DecodeComplexStructWithRawLog struct {
	BasicField BasicStruct `msgpack:"basic_field"`
	Nested1    struct {
		Field1  int                    `msgpack:"field1" convert:"stringToNumber"`
		Field2  string                 `msgpack:"field2"`
		Field4  float64                `msgpack:"field4"`
		Field5  time.Time              `msgpack:"field5"`
		RawLog  map[string]interface{} `msgpack:"raw_log"`
		Nested2 struct {
			Field1 float64                `msgpack:"field1" convert:"stringToNumber"`
			Field2 int                    `msgpack:"field2"`
			Field3 bool                   `msgpack:"field3"`
			Field4 string                 `msgpack:"field4"`
			RawLog map[string]interface{} `msgpack:"raw_log"`
		} `msgpack:"nested2"`
	} `msgpack:"nested1"`
	RawLog map[string]interface{} `msgpack:"raw_log"`
}

// MsgPackToStruct解码使用的结构体
type MsgPackToStructComplex struct {
	BasicField BasicStruct `msgpack:"basic_field" mapstructure:"basic_field"`
	Nested1    struct {
		Field1  int     `msgpack:"field1" mapstructure:"field1" convert:"stringToNumber"`
		Field2  string  `msgpack:"field2" mapstructure:"field2"`
		Field4  float64 `msgpack:"field4" mapstructure:"field4"`
		Nested2 struct {
			Field1 float64                `msgpack:"field1" mapstructure:"field1" convert:"stringToNumber"`
			Field2 int                    `msgpack:"field2" mapstructure:"field2"`
			Field3 bool                   `msgpack:"field3" mapstructure:"field3"`
			Field4 string                 `msgpack:"field4" mapstructure:"field4"`
			RawLog map[string]interface{} `msgpack:"raw_log"`
		} `msgpack:"nested2" mapstructure:"nested2"`
		RawLog map[string]interface{} `msgpack:"raw_log"`
	} `msgpack:"nested1" mapstructure:"nested1"`
	RawLog map[string]interface{} `msgpack:"raw_log" mapstructure:"raw_log"`
}


// MsgPackToStructByMapstructure解码使用的结构体
type MsgPackToStructByMapstructureComplex struct {
	BasicField BasicStruct `msgpack:"basic_field" mapstructure:"basic_field"`
	Nested1    struct {
		Field1  string  `msgpack:"field1" mapstructure:"field1" convert:"stringToNumber"`
		Field2  string  `msgpack:"field2" mapstructure:"field2"`
		Field4  float64 `msgpack:"field4" mapstructure:"field4"`
		Nested2 struct {
			Field1 string                 `msgpack:"field1" mapstructure:"field1" convert:"stringToNumber"`
			Field2 int                    `msgpack:"field2" mapstructure:"field2"`
			Field3 bool                   `msgpack:"field3" mapstructure:"field3"`
			Field4 string                 `msgpack:"field4" mapstructure:"field4"`
			RawLog map[string]interface{} `msgpack:"raw_log"`
		} `msgpack:"nested2" mapstructure:"nested2"`
		RawLog map[string]interface{} `msgpack:"raw_log"`
	} `msgpack:"nested1" mapstructure:"nested1"`
	RawLog map[string]interface{} `msgpack:"raw_log" mapstructure:"raw_log"`
}

func BenchmarkNativeMsgpackDecode_Basic(b *testing.B) {
	data := generateBasicData()
	encoded, _ := msgpack.Marshal(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result map[string]interface{}
		if err := msgpack.Unmarshal(encoded, &result); err != nil {
			panic(err)
		}
	}
}

func BenchmarkDataconvMsgpackDecode_Basic(b *testing.B) {
	data := generateBasicData()
	encoded, _ := msgpack.Marshal(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result BasicStruct
		err := MsgpackDecode(encoded, &result)
		if err != nil {
			panic(err)
		}
	}
}

func BenchmarkNativeMsgpackDecode_Complex(b *testing.B) {
	data := generateComplexData()
	encoded, _ := msgpack.Marshal(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result map[string]interface{}
		err := msgpack.Unmarshal(encoded, &result)
		if err != nil {
			panic(err)
		}
	}
}

func BenchmarkDataconvMsgpackDecode_Complex(b *testing.B) {
	data := generateComplexData()
	encoded, _ := msgpack.Marshal(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result DecodeComplexStruct
		if err := MsgpackDecode(encoded, &result); err != nil {
			panic(err)
		}
	}
}

func BenchmarkDataconvMsgpackDecode_ComplexWithRawLog(b *testing.B) {
	data := generateComplexData()
	encoded, _ := msgpack.Marshal(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result DecodeComplexStructWithRawLog
		if err := MsgpackDecode(encoded, &result); err != nil {
			panic(err)
		}
	}
}

func MsgPackToStruct(data []byte, v interface{}) error {
	var rawMap map[string]interface{}
	if err := msgpack.Unmarshal(data, &rawMap); err != nil {
		return err
	}
	if err := MapToStruct(rawMap, v); err != nil {
		return err
	}
	return nil
}

func MsgPackToStructByMapstructure(data []byte, v interface{}) error {
	var rawMap map[string]interface{}
	if err := msgpack.Unmarshal(data, &rawMap); err != nil {
		return err
	}
	if err := mapstructure.Decode(rawMap, v); err != nil {
		return err
	}

	return nil
}

func BenchmarkMsgPackToStruct_Complex(b *testing.B) {
	data := generateComplexData()
	encoded, _ := msgpack.Marshal(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result MsgPackToStructComplex
		err := MsgPackToStruct(encoded, &result)
		if err != nil {
			panic(err)
		}
	}
}

func BenchmarkMsgPackToStructByMapstructure_Complex(b *testing.B) {
	data := generateComplexData()
	encoded, _ := msgpack.Marshal(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result MsgPackToStructByMapstructureComplex
		err := MsgPackToStructByMapstructure(encoded, &result)
		if err != nil {
			panic(err)
		}
	}
}

func generateBasicData() map[string]interface{} {
	return map[string]interface{}{
		"field1":  "value1",
		"field2":  123,
		"field3":  45.67,
		"field4":  true,
		"field5":  time.Now(),
		"field6":  "value6",
		"field7":  789,
		"field8":  12.34,
		"field9":  false,
		"field10": time.Now().Add(time.Hour),
	}
}

func generateComplexData() map[string]interface{} {
	return map[string]interface{}{
		"basic_field": generateBasicData(),
		"nested1": map[string]interface{}{
			"field1": "42",
			"field2": "nested value",
			"field3": "true",
			"field4": 3.14,
			"field5": time.Now(),
			"nested2": map[string]interface{}{
				"field1": "2.718",
				"field2": 100,
				"field3": false,
				"field4": "deep value",
				"field5": time.Now().Unix(),
			},
		},
		"unknown1": "extra data",
		"unknown2": 999,
		"unknown3": map[string]interface{}{
			"key": "value",
		},
	}
}

// 优化版本基准测试结构体
type OptimizedBenchmarkStruct struct {
	Field1  string    `mapconv:"field1"`
	Field2  int       `mapconv:"field2"`
	Field3  float64   `mapconv:"field3"`
	Field4  bool      `mapconv:"field4"`
	Field5  time.Time `mapconv:"field5"`
	Field6  string    `mapconv:"field6"`
	Field7  int       `mapconv:"field7"`
	Field8  float64   `mapconv:"field8"`
	Field9  bool      `mapconv:"field9"`
	Field10 time.Time `mapconv:"field10"`
	RawLog  map[string]interface{} `rawlog:"target"`
}

type OptimizedComplexStruct struct {
	BasicField OptimizedBenchmarkStruct `mapconv:"basic_field"`
	Nested1    struct {
		Field1  int       `mapconv:"field1" convert:"stringToNumber"`
		Field2  string    `mapconv:"field2"`
		Field4  float64   `mapconv:"field4"`
		Field5  time.Time `mapconv:"field5"`
		Nested2 struct {
			Field1 float64                `mapconv:"field1" convert:"stringToNumber"`
			Field2 int                    `mapconv:"field2"`
			Field3 bool                   `mapconv:"field3"`
			Field4 string                 `mapconv:"field4"`
			RawLog map[string]interface{} `rawlog:"target"`
		} `mapconv:"nested2"`
		RawLog map[string]interface{} `rawlog:"target"`
	} `mapconv:"nested1"`
	RawLog map[string]interface{} `rawlog:"target"`
}

// 优化版本基准测试：OptimizedMapToStruct
func BenchmarkOptimizedMapToStruct_Simple(b *testing.B) {
	simpleData := map[string]interface{}{
		"field1": "value1",
		"field2": 123,
		"field3": 45.67,
		"field4": true,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result OptimizedBenchmarkStruct
		if err := OptimizedMapToStruct(simpleData, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOptimizedMapToStruct_Moderate(b *testing.B) {
	data := generateBasicData()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result OptimizedBenchmarkStruct
		if err := OptimizedMapToStruct(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOptimizedMapToStruct_Complex(b *testing.B) {
	data := generateComplexData()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result OptimizedComplexStruct
		if err := OptimizedMapToStruct(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

// 内存分配基准测试：OptimizedMapToStruct
func BenchmarkOptimizedMapToStruct_SimpleAllocs(b *testing.B) {
	simpleData := map[string]interface{}{
		"field1": "value1",
		"field2": 123,
		"field3": 45.67,
		"field4": true,
	}

	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result OptimizedBenchmarkStruct
		if err := OptimizedMapToStruct(simpleData, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOptimizedMapToStruct_ModerateAllocs(b *testing.B) {
	data := generateBasicData()

	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result OptimizedBenchmarkStruct
		if err := OptimizedMapToStruct(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkOptimizedMapToStruct_ComplexAllocs(b *testing.B) {
	data := generateComplexData()

	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result OptimizedComplexStruct
		if err := OptimizedMapToStruct(data, &result); err != nil {
			b.Fatal(err)
		}
	}
}
